The DateTime package
====================

Encapsulation of date/time values.


Function Timezones()
--------------------

Returns the list of recognized timezone names:

  >>> from DateTime import Timezones
  >>> zones = set(Timezones())

Almost all of the standard pytz timezones are included, with the exception
of some commonly-used but ambiguous abbreviations, where historical Zope
usage conflicts with the name used by pytz:

  >>> import pytz
  >>> [x for x in pytz.all_timezones if x not in zones]
  ['CET', 'EET', 'EST', 'MET', 'MST', 'WET']

Class DateTime
--------------

DateTime objects represent instants in time and provide interfaces for
controlling its representation without affecting the absolute value of
the object.

DateTime objects may be created from a wide variety of string or
numeric data, or may be computed from other DateTime objects.
DateTimes support the ability to convert their representations to many
major timezones, as well as the ability to create a DateTime object
in the context of a given timezone.

DateTime objects provide partial numerical behavior:

* Two date-time objects can be subtracted to obtain a time, in days
  between the two.

* A date-time object and a positive or negative number may be added to
  obtain a new date-time object that is the given number of days later
  than the input date-time object.

* A positive or negative number and a date-time object may be added to
  obtain a new date-time object that is the given number of days later
  than the input date-time object.

* A positive or negative number may be subtracted from a date-time
  object to obtain a new date-time object that is the given number of
  days earlier than the input date-time object.

DateTime objects may be converted to integer, long, or float numbers
of days since January 1, 1901, using the standard int, long, and float
functions (Compatibility Note: int, long and float return the number
of days since 1901 in GMT rather than local machine timezone).
DateTime objects also provide access to their value in a float format
usable with the Python time module, provided that the value of the
object falls in the range of the epoch-based time module.

A DateTime object should be considered immutable; all conversion and numeric
operations return a new DateTime object rather than modify the current object.

A DateTime object always maintains its value as an absolute UTC time,
and is represented in the context of some timezone based on the
arguments used to create the object.  A DateTime object's methods
return values based on the timezone context.

Note that in all cases the local machine timezone is used for
representation if no timezone is specified.

Constructor for DateTime
------------------------

DateTime() returns a new date-time object.  DateTimes may be created
with from zero to seven arguments:

* If the function is called with no arguments, then the current date/
  time is returned, represented in the timezone of the local machine.

* If the function is invoked with a single string argument which is a
  recognized timezone name, an object representing the current time is
  returned, represented in the specified timezone.

* If the function is invoked with a single string argument
  representing a valid date/time, an object representing that date/
  time will be returned.

  As a general rule, any date-time representation that is recognized
  and unambiguous to a resident of North America is acceptable.  (The
  reason for this qualification is that in North America, a date like:
  2/1/1994 is interpreted as February 1, 1994, while in some parts of
  the world, it is interpreted as January 2, 1994.) A date/ time
  string consists of two components, a date component and an optional
  time component, separated by one or more spaces.  If the time
  component is omitted, 12:00am is assumed.
  
  Any recognized timezone name specified as the final element of the
  date/time string will be used for computing the date/time value.
  (If you create a DateTime with the string,
  "Mar 9, 1997 1:45pm US/Pacific", the value will essentially be the
  same as if you had captured time.time() at the specified date and
  time on a machine in that timezone).  If no timezone is passed, then
  the timezone configured on the local machine will be used, **except**
  that if the date format matches ISO 8601 ('YYYY-MM-DD'), the instance
  will use UTC / GMT+0 as the timezone.

  o Returns current date/time, represented in US/Eastern:

    >>> from DateTime import DateTime
    >>> e = DateTime('US/Eastern')
    >>> e.timezone()
    'US/Eastern'

  o Returns specified time, represented in local machine zone:

    >>> x = DateTime('1997/3/9 1:45pm')
    >>> x.parts() # doctest: +ELLIPSIS
    (1997, 3, 9, 13, 45, ...)

  o Specified time in local machine zone, verbose format:

    >>> y = DateTime('Mar 9, 1997 13:45:00')
    >>> y.parts() # doctest: +ELLIPSIS
    (1997, 3, 9, 13, 45, ...)
    >>> y == x
    True
    
  o Specified time in UTC via ISO 8601 rule:
  
    >>> z = DateTime('2014-03-24')
    >>> z.parts() # doctest: +ELLIPSIS
    (2014, 3, 24, 0, 0, ...)
    >>> z.timezone()
    'GMT+0'

  The date component consists of year, month, and day values.  The
  year value must be a one-, two-, or four-digit integer.  If a one-
  or two-digit year is used, the year is assumed to be in the
  twentieth century.  The month may an integer, from 1 to 12, a month
  name, or a month abbreviation, where a period may optionally follow
  the abbreviation.  The day must be an integer from 1 to the number of
  days in the month.  The year, month, and day values may be separated
  by periods, hyphens, forward slashes, or spaces.  Extra spaces are
  permitted around the delimiters.  Year, month, and day values may be
  given in any order as long as it is possible to distinguish the
  components.  If all three components are numbers that are less than
  13, then a month-day-year ordering is assumed.

  The time component consists of hour, minute, and second values
  separated by colons.  The hour value must be an integer between 0
  and 23 inclusively.  The minute value must be an integer between 0
  and 59 inclusively.  The second value may be an integer value
  between 0 and 59.999 inclusively.  The second value or both the
  minute and second values may be omitted.  The time may be followed
  by am or pm in upper or lower case, in which case a 12-hour clock is
  assumed.

* If the DateTime function is invoked with a single numeric argument,
  the number is assumed to be either a floating point value such as
  that returned by time.time(), or a number of days after January 1,
  1901 00:00:00 UTC.

  A DateTime object is returned that represents either the GMT value
  of the time.time() float represented in the local machine's
  timezone, or that number of days after January 1, 1901.  Note that
  the number of days after 1901 need to be expressed from the
  viewpoint of the local machine's timezone.  A negative argument will
  yield a date-time value before 1901.

* If the function is invoked with two numeric arguments, then the
  first is taken to be an integer year and the second argument is
  taken to be an offset in days from the beginning of the year, in the
  context of the local machine timezone.  The date-time value returned
  is the given offset number of days from the beginning of the given
  year, represented in the timezone of the local machine.  The offset
  may be positive or negative.  Two-digit years are assumed to be in
  the twentieth century.

* If the function is invoked with two arguments, the first a float
  representing a number of seconds past the epoch in GMT (such as
  those returned by time.time()) and the second a string naming a
  recognized timezone, a DateTime with a value of that GMT time will
  be returned, represented in the given timezone.

    >>> import time
    >>> t = time.time()

  Time t represented as US/Eastern:

    >>> now_east = DateTime(t, 'US/Eastern')

  Time t represented as US/Pacific:

    >>> now_west = DateTime(t, 'US/Pacific')

  Only their representations are different:

    >>> now_east.equalTo(now_west)
    True

* If the function is invoked with three or more numeric arguments,
  then the first is taken to be an integer year, the second is taken
  to be an integer month, and the third is taken to be an integer day.
  If the combination of values is not valid, then a DateTimeError is
  raised.  One- or two-digit years up to 69 are assumed to be in the 
  21st century, whereas values 70-99 are assumed to be 20th century.
  The fourth, fifth, and sixth arguments are floating point, positive
  or negative offsets in units of hours, minutes, and days, and
  default to zero if not given.  An optional string may be given as
  the final argument to indicate timezone (the effect of this is as if
  you had taken the value of time.time() at that time on a machine in
  the specified timezone).

If a string argument passed to the DateTime constructor cannot be
parsed, it will raise SyntaxError.  Invalid date, time, or
timezone components will raise a DateTimeError.

The module function Timezones() will return a list of the timezones
recognized by the DateTime module.  Recognition of timezone names is
case-insensitive.

Instance Methods for DateTime (IDateTime interface)
---------------------------------------------------

Conversion and comparison methods
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

* ``timeTime()`` returns the date/time as a floating-point number in
  UTC, in the format used by the Python time module.  Note that it is
  possible to create date /time values with DateTime that have no
  meaningful value to the time module, and in such cases a
  DateTimeError is raised.  A DateTime object's value must generally
  be between Jan 1, 1970 (or your local machine epoch) and Jan 2038 to
  produce a valid time.time() style value.

    >>> dt = DateTime('Mar 9, 1997 13:45:00 US/Eastern')
    >>> dt.timeTime()
    *********.0

    >>> DateTime('2040/01/01 UTC').timeTime()
    2208988800.0

    >>> DateTime('1900/01/01 UTC').timeTime()
    -2208988800.0

* ``toZone(z)`` returns a DateTime with the value as the current
  object, represented in the indicated timezone:

    >>> dt.toZone('UTC')
    DateTime('1997/03/09 18:45:00 UTC')

    >>> dt.toZone('UTC').equalTo(dt)
    True

* ``isFuture()`` returns true if this object represents a date/time
  later than the time of the call:

    >>> dt.isFuture()
    False
    >>> DateTime('Jan 1 3000').isFuture() # not time-machine safe!
    True

* ``isPast()`` returns true if this object represents a date/time
  earlier than the time of the call:

    >>> dt.isPast()
    True
    >>> DateTime('Jan 1 3000').isPast() # not time-machine safe!
    False

* ``isCurrentYear()`` returns true if this object represents a
  date/time that falls within the current year, in the context of this
  object's timezone representation:

    >>> dt.isCurrentYear()
    False
    >>> DateTime().isCurrentYear()
    True

* ``isCurrentMonth()`` returns true if this object represents a
  date/time that falls within the current month, in the context of
  this object's timezone representation:

    >>> dt.isCurrentMonth()
    False
    >>> DateTime().isCurrentMonth()
    True

* ``isCurrentDay()`` returns true if this object represents a
  date/time that falls within the current day, in the context of this
  object's timezone representation:

    >>> dt.isCurrentDay()
    False
    >>> DateTime().isCurrentDay()
    True

* ``isCurrentHour()`` returns true if this object represents a
  date/time that falls within the current hour, in the context of this
  object's timezone representation:

    >>> dt.isCurrentHour()
    False

    >>> DateTime().isCurrentHour()
    True

* ``isCurrentMinute()`` returns true if this object represents a
  date/time that falls within the current minute, in the context of
  this object's timezone representation:

    >>> dt.isCurrentMinute()
    False
    >>> DateTime().isCurrentMinute()
    True

* ``isLeapYear()`` returns true if the current year (in the context of
  the object's timezone) is a leap year:

    >>> dt.isLeapYear()
    False
    >>> DateTime('Mar 8 2004').isLeapYear()
    True

* ``earliestTime()`` returns a new DateTime object that represents the
    earliest possible time (in whole seconds) that still falls within
    the current object's day, in the object's timezone context:

    >>> dt.earliestTime()
    DateTime('1997/03/09 00:00:00 US/Eastern')

* ``latestTime()`` return a new DateTime object that represents the
  latest possible time (in whole seconds) that still falls within the
  current object's day, in the object's timezone context

    >>> dt.latestTime()
    DateTime('1997/03/09 23:59:59 US/Eastern')

Component access
~~~~~~~~~~~~~~~~

* ``parts()`` returns a tuple containing the calendar year, month,
  day, hour, minute second and timezone of the object

    >>> dt.parts() # doctest: +ELLIPSIS
    (1997, 3, 9, 13, 45, ... 'US/Eastern')

* ``timezone()`` returns the timezone in which the object is represented:

    >>> dt.timezone() in Timezones()
    True

* ``tzoffset()`` returns the timezone offset for the objects timezone:

    >>> dt.tzoffset()
    -18000

* ``year()`` returns the calendar year of the object:

    >>> dt.year()
    1997

* ``month()`` returns the month of the object as an integer:

    >>> dt.month()
    3

* ``Month()`` returns the full month name:

    >>> dt.Month()
    'March'

* ``aMonth()`` returns the abbreviated month name:

    >>> dt.aMonth()
    'Mar'

* ``pMonth()`` returns the abbreviated (with period) month name:

    >>> dt.pMonth()
    'Mar.'

* ``day()`` returns the integer day:

    >>> dt.day()
    9

* ``Day()`` returns the full name of the day of the week:

    >>> dt.Day()
    'Sunday'

* ``dayOfYear()`` returns the day of the year, in context of the
  timezone representation of the object:

    >>> dt.dayOfYear()
    68

* ``aDay()`` returns the abbreviated name of the day of the week:

    >>> dt.aDay()
    'Sun'

* ``pDay()`` returns the abbreviated (with period) name of the day of
  the week:

    >>> dt.pDay()
    'Sun.'

* ``dow()`` returns the integer day of the week, where Sunday is 0:

    >>> dt.dow()
    0

* ``dow_1()`` returns the integer day of the week, where sunday is 1:

    >>> dt.dow_1()
    1

* ``h_12()`` returns the 12-hour clock representation of the hour:

    >>> dt.h_12()
    1

* ``h_24()`` returns the 24-hour clock representation of the hour:

    >>> dt.h_24()
    13

* ``ampm()`` returns the appropriate time modifier (am or pm):

    >>> dt.ampm()
    'pm'

* ``hour()`` returns the 24-hour clock representation of the hour:

    >>> dt.hour()
    13

* ``minute()`` returns the minute:

    >>> dt.minute()
    45

* ``second()`` returns the second:

    >>> dt.second() == 0
    True

* ``millis()`` returns the milliseconds since the epoch in GMT.

    >>> dt.millis() == *********000
    True

strftime()
~~~~~~~~~~

See ``tests/test_datetime.py``.

General formats from previous DateTime
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

* ``Date()`` return the date string for the object:

    >>> dt.Date()
    '1997/03/09'

* ``Time()`` returns the time string for an object to the nearest
  second:

    >>> dt.Time()
    '13:45:00'

* ``TimeMinutes()`` returns the time string for an object not showing
  seconds:

    >>> dt.TimeMinutes()
    '13:45'

* ``AMPM()`` returns the time string for an object to the nearest second:

    >>> dt.AMPM()
    '01:45:00 pm'

* ``AMPMMinutes()`` returns the time string for an object not showing
  seconds:

    >>> dt.AMPMMinutes()
    '01:45 pm'

* ``PreciseTime()`` returns the time string for the object:

    >>> dt.PreciseTime()
    '13:45:00.000'

* ``PreciseAMPM()`` returns the time string for the object:

    >>> dt.PreciseAMPM()
    '01:45:00.000 pm'

* ``yy()`` returns the calendar year as a 2 digit string

    >>> dt.yy()
    '97'

* ``mm()`` returns the month as a 2 digit string

    >>> dt.mm()
    '03'

* ``dd()`` returns the day as a 2 digit string:

    >>> dt.dd()
    '09'

* ``rfc822()`` returns the date in RFC 822 format:

    >>> dt.rfc822()
    'Sun, 09 Mar 1997 13:45:00 -0500'

New formats
~~~~~~~~~~~

* ``fCommon()`` returns a string representing the object's value in
  the format: March 9, 1997 1:45 pm:

    >>> dt.fCommon()
    'March 9, 1997 1:45 pm'

* ``fCommonZ()`` returns a string representing the object's value in
  the format: March 9, 1997 1:45 pm US/Eastern:

    >>> dt.fCommonZ()
    'March 9, 1997 1:45 pm US/Eastern'

* ``aCommon()`` returns a string representing the object's value in
  the format: Mar 9, 1997 1:45 pm:

    >>> dt.aCommon()
    'Mar 9, 1997 1:45 pm'

* ``aCommonZ()`` return a string representing the object's value in
  the format: Mar 9, 1997 1:45 pm US/Eastern:

    >>> dt.aCommonZ()
    'Mar 9, 1997 1:45 pm US/Eastern'

* ``pCommon()`` returns a string representing the object's value in
  the format Mar. 9, 1997 1:45 pm:

    >>> dt.pCommon()
    'Mar. 9, 1997 1:45 pm'

* ``pCommonZ()`` returns a string representing the object's value in
  the format: Mar. 9, 1997 1:45 pm US/Eastern:

    >>> dt.pCommonZ()
    'Mar. 9, 1997 1:45 pm US/Eastern'

* ``ISO()`` returns a string with the date/time in ISO format.  Note:
  this is not ISO 8601-format! See the ISO8601 and HTML4 methods below
  for ISO 8601-compliant output.  Dates are output as: YYYY-MM-DD HH:MM:SS

    >>> dt.ISO()
    '1997-03-09 13:45:00'

* ``ISO8601()`` returns the object in ISO 8601-compatible format
  containing the date, time with seconds-precision and the time zone
  identifier - see http://www.w3.org/TR/NOTE-datetime.  Dates are
  output as: YYYY-MM-DDTHH:MM:SSTZD (T is a literal character, TZD is
  Time Zone Designator, format +HH:MM or -HH:MM).

  The ``HTML4()`` method below offers the same formatting, but
  converts to UTC before returning the value and sets the TZD"Z"

    >>> dt.ISO8601()
    '1997-03-09T13:45:00-05:00'


* ``HTML4()`` returns the object in the format used in the HTML4.0
  specification, one of the standard forms in ISO8601.  See
  http://www.w3.org/TR/NOTE-datetime.  Dates are output as:
  YYYY-MM-DDTHH:MM:SSZ (T, Z are literal characters, the time is in
  UTC.):

    >>> dt.HTML4()
    '1997-03-09T18:45:00Z'

* ``JulianDay()`` returns the Julian day according to
  http://www.tondering.dk/claus/cal/node3.html#sec-calcjd

    >>> dt.JulianDay()
    2450517

* ``week()`` returns the week number according to ISO
  see http://www.tondering.dk/claus/cal/node6.html#SECTION00670000000000000000

    >>> dt.week()
    10

Deprecated API
~~~~~~~~~~~~~~

* DayOfWeek(): see Day()

* Day_(): see pDay()

* Mon(): see aMonth()

* Mon_(): see pMonth

General Services Provided by DateTime
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

DateTimes can be repr()'ed; the result will be a string indicating how
to make a DateTime object like this:

  >>> repr(dt)
  "DateTime('1997/03/09 13:45:00 US/Eastern')"

When we convert them into a string, we get a nicer string that could
actually be shown to a user:

  >>> str(dt)
  '1997/03/09 13:45:00 US/Eastern'

The hash value of a DateTime is based on the date and time and is
equal for different representations of the DateTime:

  >>> hash(dt)
  3618678
  >>> hash(dt.toZone('UTC'))
  3618678

DateTime objects can be compared to other DateTime objects OR floating
point numbers such as the ones which are returned by the Python time
module by using the equalTo method. Using this API, True is returned if the
object represents a date/time equal to the specified DateTime or time module
style time:

  >>> dt.equalTo(dt)
  True
  >>> dt.equalTo(dt.toZone('UTC'))
  True
  >>> dt.equalTo(dt.timeTime())
  True
  >>> dt.equalTo(DateTime())
  False

Same goes for inequalities:

  >>> dt.notEqualTo(dt)
  False
  >>> dt.notEqualTo(dt.toZone('UTC'))
  False
  >>> dt.notEqualTo(dt.timeTime())
  False
  >>> dt.notEqualTo(DateTime())
  True

Normal equality operations only work with DateTime objects and take the
timezone setting into account:

  >>> dt == dt
  True
  >>> dt == dt.toZone('UTC')
  False
  >>> dt == DateTime()
  False

  >>> dt != dt
  False
  >>> dt != dt.toZone('UTC')
  True
  >>> dt != DateTime()
  True

But the other comparison operations compare the referenced moment in time and
not the representation itself:

  >>> dt > dt
  False
  >>> DateTime() > dt
  True
  >>> dt > DateTime().timeTime()
  False
  >>> DateTime().timeTime() > dt
  True

  >>> dt.greaterThan(dt)
  False
  >>> DateTime().greaterThan(dt)
  True
  >>> dt.greaterThan(DateTime().timeTime())
  False

  >>> dt >= dt
  True
  >>> DateTime() >= dt
  True
  >>> dt >= DateTime().timeTime()
  False
  >>> DateTime().timeTime() >= dt
  True

  >>> dt.greaterThanEqualTo(dt)
  True
  >>> DateTime().greaterThanEqualTo(dt)
  True
  >>> dt.greaterThanEqualTo(DateTime().timeTime())
  False

  >>> dt < dt
  False
  >>> DateTime() < dt
  False
  >>> dt < DateTime().timeTime()
  True
  >>> DateTime().timeTime() < dt
  False

  >>> dt.lessThan(dt)
  False
  >>> DateTime().lessThan(dt)
  False
  >>> dt.lessThan(DateTime().timeTime())
  True

  >>> dt <= dt
  True
  >>> DateTime() <= dt
  False
  >>> dt <= DateTime().timeTime()
  True
  >>> DateTime().timeTime() <= dt
  False

  >>> dt.lessThanEqualTo(dt)
  True
  >>> DateTime().lessThanEqualTo(dt)
  False
  >>> dt.lessThanEqualTo(DateTime().timeTime())
  True

Numeric Services Provided by DateTime
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

A DateTime may be added to a number and a number may be added to a
DateTime:

  >>> dt + 5
  DateTime('1997/03/14 13:45:00 US/Eastern')
  >>> 5 + dt
  DateTime('1997/03/14 13:45:00 US/Eastern')

Two DateTimes cannot be added:

  >>> from DateTime.interfaces import DateTimeError
  >>> try:
  ...     dt + dt
  ...     print('fail')
  ... except DateTimeError:
  ...     print('ok')
  ok

Either a DateTime or a number may be subtracted from a DateTime,
however, a DateTime may not be subtracted from a number:

  >>> DateTime('1997/03/10 13:45 US/Eastern') - dt
  1.0
  >>> dt - 1
  DateTime('1997/03/08 13:45:00 US/Eastern')
  >>> 1 - dt
  Traceback (most recent call last):
  ...
  TypeError: unsupported operand type(s) for -: 'int' and 'DateTime'

DateTimes can also be converted to integers (number of seconds since
the epoch) and floats:

  >>> int(dt)
  *********
  >>> float(dt)
  *********.0
