# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union, Iterable
from typing_extensions import Literal, Required, TypedDict

from .content_block import ContentBlock
from .text_block_param import Text<PERSON><PERSON><PERSON>aram
from .image_block_param import <PERSON><PERSON><PERSON><PERSON>aram
from .document_block_param import <PERSON>ument<PERSON><PERSON>Param
from .thinking_block_param import Thinking<PERSON><PERSON><PERSON>ara<PERSON>
from .tool_use_block_param import Tool<PERSON><PERSON><PERSON><PERSON><PERSON>ara<PERSON>
from .tool_result_block_param import ToolResultBlockParam
from .server_tool_use_block_param import ServerToolUseBlockParam
from .redacted_thinking_block_param import RedactedThinking<PERSON>lockParam
from .web_search_tool_result_block_param import WebSearchToolResultBlockParam

__all__ = ["MessageParam"]


class MessageParam(TypedDict, total=False):
    content: Required[
        Union[
            str,
            Iterable[
                Union[
                    TextBlockParam,
                    ImageBlockParam,
                    DocumentBlockParam,
                    ThinkingBlockParam,
                    Redacted<PERSON><PERSON>king<PERSON>lockParam,
                    ToolUseBlockParam,
                    ToolResultBlockParam,
                    ServerToolUseBlockParam,
                    WebSearchToolResultBlockParam,
                    ContentBlock,
                ]
            ],
        ]
    ]

    role: Required[Literal["user", "assistant"]]
