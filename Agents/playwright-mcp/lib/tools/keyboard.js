/**
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { z } from 'zod';
import { defineTool } from './tool.js';
const pressKey = captureSnapshot => defineTool({
    capability: 'core',
    schema: {
        name: 'browser_press_key',
        title: 'Press a key',
        description: 'Press a key on the keyboard',
        inputSchema: z.object({
            key: z.string().describe('Name of the key to press or a character to generate, such as `ArrowLeft` or `a`'),
        }),
        type: 'destructive',
    },
    handle: async (context, params) => {
        const tab = context.currentTabOrDie();
        const code = [
            `// Press ${params.key}`,
            `await page.keyboard.press('${params.key}');`,
        ];
        const action = () => tab.page.keyboard.press(params.key);
        return {
            code,
            action,
            captureSnapshot,
            waitForNetwork: true
        };
    },
});
export default (captureSnapshot) => [
    pressKey(captureSnapshot),
];
