#!/usr/bin/env python3
"""
Test SQLMap Fix
Quick test to verify SQLMap command line options are correct
"""

import sys
import asyncio
import subprocess
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import config

async def test_sqlmap_version():
    """Test SQLMap version and basic functionality"""
    print("🔍 Testing SQLMap Installation")
    print("="*40)
    
    if not config:
        print("❌ Configuration not loaded")
        return False
    
    sqlmap_path = config.sqli.sqlmap_path
    print(f"📍 SQLMap path: {sqlmap_path}")
    
    if not Path(sqlmap_path).exists():
        print(f"❌ SQLMap not found at {sqlmap_path}")
        return False
    
    try:
        # Test SQLMap version
        print("🧪 Testing SQLMap --version...")
        process = await asyncio.create_subprocess_exec(
            sqlmap_path, "--version",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=10)
        
        if process.returncode == 0:
            version = stdout.decode().strip()
            print(f"✅ SQLMap version: {version}")
        else:
            print(f"❌ SQLMap version check failed: {stderr.decode()}")
            return False
        
        # Test SQLMap help to check available options
        print("🧪 Testing SQLMap --help...")
        process = await asyncio.create_subprocess_exec(
            sqlmap_path, "--help",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=10)
        
        if process.returncode == 0:
            help_text = stdout.decode()
            
            # Check for key options we use
            required_options = ["--batch", "--level", "--risk", "--timeout", "--output-dir", "--threads", "--technique"]
            missing_options = []
            
            for option in required_options:
                if option not in help_text:
                    missing_options.append(option)
            
            if missing_options:
                print(f"⚠️  Missing options: {missing_options}")
            else:
                print("✅ All required options available")
            
            # Check for problematic options
            if "--format" in help_text:
                print("⚠️  --format option is available (unexpected)")
            else:
                print("✅ --format option not found (correct - this was causing the error)")
                
        else:
            print(f"❌ SQLMap help check failed: {stderr.decode()}")
            return False
        
        return True
        
    except asyncio.TimeoutError:
        print("❌ SQLMap test timed out")
        return False
    except Exception as e:
        print(f"❌ SQLMap test error: {e}")
        return False

def test_sqlmap_command_generation():
    """Test the command generation logic"""
    print("\n🔧 Testing SQLMap Command Generation")
    print("="*40)
    
    if not config:
        print("❌ Configuration not loaded")
        return False
    
    # Simulate the command that would be generated
    target_url = "https://juice-shop.herokuapp.com"
    
    cmd = [
        config.sqli.sqlmap_path,
        "-u", target_url,
        "--batch",
        "--level", str(min(config.sqli.level, 3)),
        "--risk", str(min(config.sqli.risk, 2)),
        "--timeout", str(min(config.sqli.timeout, 120)),
        "--output-dir", str(Path(config.general.reports_dir) / "sqlmap"),
        "--threads", "2",
        "--technique", "BEUST"
    ]
    
    print("📝 Generated SQLMap command:")
    print("   " + " ".join(cmd))
    
    # Check for problematic options
    if "--format" in cmd:
        print("❌ Command contains --format option (this was causing the error)")
        return False
    else:
        print("✅ Command does not contain --format option")
    
    # Validate command structure
    if len(cmd) < 5:
        print("❌ Command too short")
        return False
    
    if not cmd[1] == "-u":
        print("❌ URL option not properly formatted")
        return False
    
    if not cmd[2] == target_url:
        print("❌ Target URL not properly set")
        return False
    
    print("✅ Command structure looks correct")
    return True

async def test_dry_run():
    """Test SQLMap with a dry run (just validate options)"""
    print("\n🧪 Testing SQLMap Dry Run")
    print("="*40)
    
    if not config:
        print("❌ Configuration not loaded")
        return False
    
    try:
        # Test with a simple, safe command that just validates options
        cmd = [
            config.sqli.sqlmap_path,
            "--batch",
            "--help"  # This will just show help and exit
        ]
        
        print("🔄 Running SQLMap dry run...")
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=15)
        
        if process.returncode == 0:
            print("✅ SQLMap dry run successful")
            return True
        else:
            print(f"❌ SQLMap dry run failed: {stderr.decode()[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ SQLMap dry run error: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 SQLMap Fix Verification")
    print("="*50)
    
    tests = [
        ("SQLMap Version Test", test_sqlmap_version),
        ("Command Generation Test", test_sqlmap_command_generation),
        ("SQLMap Dry Run Test", test_dry_run)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
                failed += 1
                
        except Exception as e:
            print(f"❌ {test_name}: CRASHED - {e}")
            failed += 1
    
    print("\n" + "="*50)
    print("📊 Test Results Summary")
    print("="*50)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Total: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed! SQLMap should work correctly now.")
        print("\n🚀 Next steps:")
        print("1. Run: python main.py https://juice-shop.herokuapp.com --vulns sqli")
        print("2. Check logs for SQLMap execution")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please check SQLMap installation.")
    
    return failed == 0

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
