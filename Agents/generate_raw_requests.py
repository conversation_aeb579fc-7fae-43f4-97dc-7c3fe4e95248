#!/usr/bin/env python3
"""
Generate Complete Raw HTTP Requests
Creates comprehensive raw requests for testing VAPT agents
"""

import sys
import json
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from utils.raw_request_generator import RawRequestGenerator

def generate_juice_shop_complete_report():
    """Generate a complete preprocessor report with proper raw requests"""
    
    # Generate all raw requests
    juice_requests = RawRequestGenerator.generate_juice_shop_requests()
    sqli_requests = RawRequestGenerator.generate_sql_injection_requests("https://juice-shop.herokuapp.com")
    xss_requests = RawRequestGenerator.generate_xss_requests("https://juice-shop.herokuapp.com")
    
    # Combine all requests
    all_requests = juice_requests + sqli_requests[:5] + xss_requests[:5]  # Limit for demo
    
    # Create mock network logs
    network_logs = []
    for i, request in enumerate(juice_requests[:10]):  # First 10 requests
        lines = request.split('\r\n')
        if lines:
            method_line = lines[0]
            method, path, _ = method_line.split(' ', 2)
            
            # Extract headers
            headers = {}
            for line in lines[1:]:
                if line.strip() == '':
                    break
                if ':' in line:
                    key, value = line.split(':', 1)
                    headers[key.strip()] = value.strip()
            
            # Create network log entry
            network_log = {
                "url": f"https://juice-shop.herokuapp.com{path}",
                "method": method,
                "status": 200,
                "statusText": "OK",
                "headers": headers,
                "requestHeaders": headers,
                "responseHeaders": {
                    "content-type": "application/json" if "api" in path else "text/html",
                    "server": "nginx/1.18.0",
                    "x-powered-by": "Express"
                },
                "timestamp": f"2025-07-19T05:30:{10+i:02d}.000Z",
                "responseSize": 1024 + i * 100
            }
            
            # Add POST data if present
            if method == "POST" and len(lines) > len(headers) + 2:
                body_start = len(headers) + 2
                body = '\r\n'.join(lines[body_start:])
                network_log["postData"] = body
            
            network_logs.append(network_log)
    
    # Create complete report
    complete_report = {
        "metadata": {
            "target_url": "https://juice-shop.herokuapp.com",
            "start_time": "2025-07-19T05:30:00.000000",
            "end_time": "2025-07-19T05:32:00.000000",
            "duration": "0:02:00.000000",
            "session_id": "20250719_053000_complete"
        },
        "recon": [
            {
                "task": "Initial Reconnaissance",
                "timestamp": "2025-07-19T05:30:15.000000",
                "response": "OWASP Juice Shop - Complete reconnaissance performed. Identified e-commerce SPA with Angular frontend, Express.js backend, multiple API endpoints for user management, product catalog, shopping cart, and administrative functions. Detected potential injection points in login, search, feedback, and user registration forms.",
                "status": "completed"
            },
            {
                "task": "Comprehensive Site Crawl",
                "timestamp": "2025-07-19T05:30:45.000000",
                "response": "Discovered 25+ endpoints including REST API routes, authentication endpoints, file upload functionality, and administrative interfaces. Mapped complete application structure with user flows.",
                "status": "completed"
            },
            {
                "task": "Component Interaction Testing",
                "timestamp": "2025-07-19T05:31:15.000000",
                "response": "Tested all interactive components: login forms, search functionality, product catalog, shopping cart, user registration, feedback submission, and file upload. Captured complete request/response cycles.",
                "status": "completed"
            },
            {
                "task": "Authentication Flow Analysis",
                "timestamp": "2025-07-19T05:31:45.000000",
                "response": "Analyzed JWT-based authentication, session management, password reset flows, and administrative access controls. Identified multiple potential security issues.",
                "status": "completed"
            }
        ],
        "network_logs": network_logs,
        "console_logs": [
            {
                "level": "info",
                "message": "Angular application loaded successfully",
                "timestamp": "2025-07-19T05:30:05.000Z",
                "source": "console-api"
            },
            {
                "level": "warning",
                "message": "Deprecated API endpoint used: /rest/user/login",
                "timestamp": "2025-07-19T05:30:20.000Z",
                "source": "console-api"
            },
            {
                "level": "error",
                "message": "CORS policy violation detected",
                "timestamp": "2025-07-19T05:30:35.000Z",
                "source": "console-api"
            }
        ],
        "raw_requests": all_requests,
        "crawl_data": {
            "sitemap": {
                "/": "Main product catalog page",
                "/login": "User authentication",
                "/register": "User registration",
                "/profile": "User profile management",
                "/basket": "Shopping cart",
                "/administration": "Admin panel",
                "/rest/products/search": "Product search API",
                "/rest/user/login": "Login API",
                "/api/Users/": "User management API",
                "/api/Feedbacks/": "Feedback API",
                "/api/BasketItems/": "Cart management API"
            },
            "visited_urls": [
                "https://juice-shop.herokuapp.com/",
                "https://juice-shop.herokuapp.com/login",
                "https://juice-shop.herokuapp.com/register",
                "https://juice-shop.herokuapp.com/profile",
                "https://juice-shop.herokuapp.com/basket",
                "https://juice-shop.herokuapp.com/administration",
                "https://juice-shop.herokuapp.com/rest/products/search",
                "https://juice-shop.herokuapp.com/rest/user/login",
                "https://juice-shop.herokuapp.com/api/Users/",
                "https://juice-shop.herokuapp.com/api/Feedbacks/"
            ],
            "orphan_pages": [
                "https://juice-shop.herokuapp.com/ftp",
                "https://juice-shop.herokuapp.com/encryptionkeys"
            ]
        },
        "component_interactions": [
            {
                "type": "form_submit",
                "element": "login_form",
                "action": "/rest/user/login",
                "method": "POST",
                "timestamp": "2025-07-19T05:30:25.000Z"
            },
            {
                "type": "input_fill",
                "element": "search_box",
                "value": "apple",
                "timestamp": "2025-07-19T05:30:40.000Z"
            },
            {
                "type": "button_click",
                "element": "add_to_cart",
                "product_id": "1",
                "timestamp": "2025-07-19T05:31:00.000Z"
            }
        ],
        "session_states": [
            {
                "timestamp": "2025-07-19T05:30:00.000Z",
                "authenticated": False,
                "cart_items": 0,
                "current_page": "/"
            },
            {
                "timestamp": "2025-07-19T05:30:30.000Z",
                "authenticated": True,
                "user_id": "1",
                "cart_items": 0,
                "current_page": "/profile"
            },
            {
                "timestamp": "2025-07-19T05:31:30.000Z",
                "authenticated": True,
                "user_id": "1",
                "cart_items": 2,
                "current_page": "/basket"
            }
        ],
        "navigation_paths": [
            {
                "path": "/ -> /login -> /profile",
                "duration": "0:00:30",
                "successful": True
            },
            {
                "path": "/profile -> /basket -> /administration",
                "duration": "0:01:00",
                "successful": False,
                "error": "Access denied"
            }
        ]
    }
    
    return complete_report

def main():
    """Main function"""
    print("🚀 Generating Complete Raw HTTP Requests")
    print("="*50)
    
    # Create reports directory
    reports_dir = Path("reports")
    reports_dir.mkdir(exist_ok=True)
    
    # Generate complete report
    print("📊 Generating complete Juice Shop report...")
    complete_report = generate_juice_shop_complete_report()
    
    # Save complete report
    report_filename = "preprocessor_report_juice-shop.herokuapp.com_complete.json"
    report_path = reports_dir / report_filename
    
    with open(report_path, 'w') as f:
        json.dump(complete_report, f, indent=2)
    
    print(f"✅ Complete report saved: {report_path}")
    print(f"📊 Generated {len(complete_report['raw_requests'])} raw requests")
    print(f"🌐 Generated {len(complete_report['network_logs'])} network logs")
    
    # Generate individual request files
    print("\n📝 Generating individual request files...")
    
    # Juice Shop requests
    juice_requests = RawRequestGenerator.generate_juice_shop_requests()
    RawRequestGenerator.save_requests_to_file(juice_requests, "juice_shop_requests.txt")
    
    # SQL injection requests
    sqli_requests = RawRequestGenerator.generate_sql_injection_requests("https://juice-shop.herokuapp.com")
    RawRequestGenerator.save_requests_to_file(sqli_requests, "sqli_test_requests.txt")
    
    # XSS requests
    xss_requests = RawRequestGenerator.generate_xss_requests("https://juice-shop.herokuapp.com")
    RawRequestGenerator.save_requests_to_file(xss_requests, "xss_test_requests.txt")
    
    print("\n🎯 Usage:")
    print(f"1. Test with complete report:")
    print(f"   python main.py --report {report_path}")
    print(f"2. Test SQLi agent:")
    print(f"   python main.py --report {report_path} --vulns sqli")
    print(f"3. Test XSS agent:")
    print(f"   python main.py --report {report_path} --vulns xss")
    
    print("\n✅ Raw request generation completed!")

if __name__ == "__main__":
    main()
