"""
VAPT AI Tool Configuration Settings
Handles all configuration loading and validation
"""

import os
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class LLMConfig(BaseModel):
    """LLM Configuration"""
    provider: str = Field(default="gemini", description="LLM provider (gemini, openai, anthropic)")
    model: str = Field(default="gemini-1.5-pro", description="Model name")
    api_key: str = Field(description="API key for the LLM provider")
    max_tokens: int = Field(default=4000, description="Maximum tokens")
    temperature: float = Field(default=0.1, description="Temperature for generation")
    timeout: int = Field(default=60, description="Request timeout in seconds")
    
    @validator('api_key')
    def validate_api_key(cls, v):
        if not v or v.startswith('your_'):
            raise ValueError("Valid API key is required")
        return v

class GeneralConfig(BaseModel):
    """General Configuration"""
    debug_mode: bool = Field(default=False, description="Enable debug mode")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    timeout_seconds: int = Field(default=300, description="General timeout")
    reports_dir: str = Field(default="reports", description="Reports directory")
    logs_dir: str = Field(default="logs", description="Logs directory")

class PreprocessorConfig(BaseModel):
    """Preprocessor Configuration"""
    max_pages: int = Field(default=10, description="Maximum pages to crawl")
    headless: bool = Field(default=True, description="Run browser in headless mode")
    timeout: int = Field(default=60, description="Preprocessor timeout")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    delay_between_requests: float = Field(default=1.0, description="Delay between requests")

class PlaywrightConfig(BaseModel):
    """Playwright MCP Configuration"""
    port: int = Field(default=8931, description="MCP server port")
    browser: str = Field(default="chrome", description="Browser type")
    node_env: str = Field(default="production", description="Node environment")
    timeout_seconds: int = Field(default=30, description="Playwright timeout")

class SQLiConfig(BaseModel):
    """SQLi Agent Configuration"""
    enabled: bool = Field(default=True, description="Enable SQLi testing")
    sqlmap_path: str = Field(default="/usr/bin/sqlmap", description="SQLMap executable path")
    timeout: int = Field(default=300, description="SQLi testing timeout")
    level: int = Field(default=5, description="SQLMap level")
    risk: int = Field(default=3, description="SQLMap risk")

class XSSConfig(BaseModel):
    """XSS Agent Configuration"""
    enabled: bool = Field(default=True, description="Enable XSS testing")
    timeout: int = Field(default=60, description="XSS testing timeout")
    max_payloads: int = Field(default=50, description="Maximum XSS payloads to test")

class SecurityConfig(BaseModel):
    """Security Configuration"""
    enable_audit_logging: bool = Field(default=True, description="Enable audit logging")
    filter_sensitive_data: bool = Field(default=True, description="Filter sensitive data from logs")

class VAPTConfig(BaseModel):
    """Main VAPT Configuration"""
    llm: LLMConfig
    general: GeneralConfig
    preprocessor: PreprocessorConfig
    playwright: PlaywrightConfig
    sqli: SQLiConfig
    xss: XSSConfig
    security: SecurityConfig

def load_config() -> VAPTConfig:
    """Load configuration from environment variables"""
    
    # Determine API key based on provider
    provider = os.getenv('LLM_PROVIDER', 'gemini').lower()
    api_key_map = {
        'gemini': os.getenv('GEMINI_API_KEY'),
        'openai': os.getenv('OPENAI_API_KEY'),
        'anthropic': os.getenv('ANTHROPIC_API_KEY')
    }
    
    api_key = api_key_map.get(provider)
    if not api_key:
        raise ValueError(f"API key not found for provider: {provider}")
    
    config_data = {
        'llm': {
            'provider': provider,
            'model': os.getenv('LLM_MODEL', 'gemini-1.5-pro'),
            'api_key': api_key,
            'max_tokens': int(os.getenv('LLM_MAX_TOKENS', '4000')),
            'temperature': float(os.getenv('LLM_TEMPERATURE', '0.1')),
            'timeout': int(os.getenv('LLM_TIMEOUT', '60'))
        },
        'general': {
            'debug_mode': os.getenv('DEBUG_MODE', 'false').lower() == 'true',
            'max_retries': int(os.getenv('MAX_RETRIES', '3')),
            'timeout_seconds': int(os.getenv('TIMEOUT_SECONDS', '300')),
            'reports_dir': os.getenv('REPORTS_DIR', 'reports'),
            'logs_dir': os.getenv('LOGS_DIR', 'logs')
        },
        'preprocessor': {
            'max_pages': int(os.getenv('PREPROCESSOR_MAX_PAGES', '10')),
            'headless': os.getenv('PREPROCESSOR_HEADLESS', 'true').lower() == 'true',
            'timeout': int(os.getenv('PREPROCESSOR_TIMEOUT', '60')),
            'max_retries': int(os.getenv('PREPROCESSOR_MAX_RETRIES', '3')),
            'delay_between_requests': float(os.getenv('PREPROCESSOR_DELAY_BETWEEN_REQUESTS', '1.0'))
        },
        'playwright': {
            'port': int(os.getenv('PLAYWRIGHT_MCP_PORT', '8931')),
            'browser': os.getenv('PLAYWRIGHT_MCP_BROWSER', 'chrome'),
            'node_env': os.getenv('PLAYWRIGHT_NODE_ENV', 'production'),
            'timeout_seconds': int(os.getenv('PLAYWRIGHT_TIMEOUT_SECONDS', '30'))
        },
        'sqli': {
            'enabled': os.getenv('SQLI_ENABLED', 'true').lower() == 'true',
            'sqlmap_path': os.getenv('SQLI_SQLMAP_PATH', '/usr/bin/sqlmap'),
            'timeout': int(os.getenv('SQLI_TIMEOUT', '300')),
            'level': int(os.getenv('SQLI_LEVEL', '5')),
            'risk': int(os.getenv('SQLI_RISK', '3'))
        },
        'xss': {
            'enabled': os.getenv('XSS_ENABLED', 'true').lower() == 'true',
            'timeout': int(os.getenv('XSS_TIMEOUT', '60')),
            'max_payloads': int(os.getenv('XSS_MAX_PAYLOADS', '50'))
        },
        'security': {
            'enable_audit_logging': os.getenv('ENABLE_AUDIT_LOGGING', 'true').lower() == 'true',
            'filter_sensitive_data': os.getenv('FILTER_SENSITIVE_DATA', 'true').lower() == 'true'
        }
    }
    
    return VAPTConfig(**config_data)

# Global configuration instance
try:
    config = load_config()
except Exception as e:
    print(f"Configuration loading failed: {e}")
    print("Please check your .env file and ensure all required values are set")
    config = None


def validate_config():
    """Validate configuration settings with comprehensive checks"""
    if not config:
        raise ConfigurationError("Configuration not loaded")

    validation_results = {
        "directories": [],
        "tools": [],
        "network": [],
        "warnings": [],
        "errors": []
    }

    # Validate required directories
    required_dirs = [config.general.reports_dir, config.general.logs_dir]
    for dir_path in required_dirs:
        try:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            validation_results["directories"].append(f"✅ {dir_path}")
        except Exception as e:
            validation_results["errors"].append(f"❌ Failed to create {dir_path}: {e}")

    # Validate SQLMap
    if config.sqli.sqlmap_path:
        if Path(config.sqli.sqlmap_path).exists():
            validation_results["tools"].append(f"✅ SQLMap found at {config.sqli.sqlmap_path}")
        else:
            validation_results["warnings"].append(f"⚠️  SQLMap not found at {config.sqli.sqlmap_path}")
    else:
        validation_results["warnings"].append("⚠️  SQLMap path not configured")

    # Validate playwright-mcp
    playwright_mcp_paths = [
        Path("playwright-mcp/cli.js"),
        Path("../playwright-mcp/cli.js"),
        Path("../../playwright-mcp/cli.js")
    ]

    playwright_found = False
    for path in playwright_mcp_paths:
        if path.exists():
            validation_results["tools"].append(f"✅ playwright-mcp found at {path}")
            playwright_found = True
            break

    if not playwright_found:
        validation_results["warnings"].append("⚠️  playwright-mcp not found - will use fallback browser agent")

    # Validate network libraries
    try:
        import aiohttp
        validation_results["network"].append("✅ aiohttp available for fallback browser")
    except ImportError:
        validation_results["errors"].append("❌ aiohttp not available - fallback browser will fail")

    try:
        import requests
        validation_results["network"].append("✅ requests library available")
    except ImportError:
        validation_results["warnings"].append("⚠️  requests library not available")

    # Print validation results
    logger.info("Configuration Validation Results:")
    logger.info("="*50)

    for category, results in validation_results.items():
        if results and category not in ["warnings", "errors"]:
            logger.info(f"{category.upper()}:")
            for result in results:
                logger.info(f"  {result}")

    if validation_results["warnings"]:
        logger.warning("WARNINGS:")
        for warning in validation_results["warnings"]:
            logger.warning(f"  {warning}")

    if validation_results["errors"]:
        logger.error("ERRORS:")
        for error in validation_results["errors"]:
            logger.error(f"  {error}")
        raise ConfigurationError(f"Configuration validation failed with {len(validation_results['errors'])} errors")

    logger.info("Configuration validation completed successfully")
    return validation_results
