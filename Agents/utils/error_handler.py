"""
Error handling utilities for VAPT AI Tool
Provides comprehensive error handling and recovery mechanisms
"""

import logging
import traceback
import functools
from typing import Optional, Dict, Any, Callable
from datetime import datetime

class VAPTError(Exception):
    """Base exception for VAPT AI Tool"""
    def __init__(self, message: str, component: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.component = component
        self.details = details or {}
        self.timestamp = datetime.now()
        super().__init__(self.message)

class LeadAgentError(VAPTError):
    """Lead Agent specific errors"""
    pass

class PreprocessorError(VAPTError):
    """Preprocessor specific errors"""
    pass

class SQLiAgentError(VAPTError):
    """SQLi Agent specific errors"""
    pass

class XSSAgentError(VAPTError):
    """XSS Agent specific errors"""
    pass

class LLMError(VAPTError):
    """LLM provider specific errors"""
    pass

class MCPError(VAPTError):
    """MCP (Model Context Protocol) specific errors"""
    pass

class ErrorHandler:
    """Centralized error handling and recovery"""
    
    def __init__(self, component_name: str):
        self.component_name = component_name
        self.logger = logging.getLogger(f"error_handler.{component_name}")
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None) -> Optional[Any]:
        """Handle and log errors with context"""
        context = context or {}
        
        error_info = {
            'component': self.component_name,
            'error_type': type(error).__name__,
            'message': str(error),
            'context': context,
            'timestamp': datetime.now().isoformat(),
            'traceback': traceback.format_exc()
        }
        
        # Log based on error severity
        if isinstance(error, (LeadAgentError, PreprocessorError, SQLiAgentError, XSSAgentError)):
            self.logger.error(f"Component error in {self.component_name}: {error_info}")
        elif isinstance(error, (LLMError, MCPError)):
            self.logger.warning(f"Service error in {self.component_name}: {error_info}")
        else:
            self.logger.error(f"Unexpected error in {self.component_name}: {error_info}")
        
        return None
    
    def with_retry(self, func: Callable, max_retries: int = 3, delay: float = 1.0):
        """Execute function with retry logic"""
        import time
        
        for attempt in range(max_retries):
            try:
                return func()
            except Exception as e:
                if attempt == max_retries - 1:
                    self.handle_error(e, {'attempt': attempt + 1, 'max_retries': max_retries})
                    raise
                
                self.logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s: {e}")
                time.sleep(delay)
        
        return None

def error_handler(component: str = None):
    """Decorator for automatic error handling"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            handler = ErrorHandler(component or func.__module__)
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = {
                    'function': func.__name__,
                    'args': str(args)[:200],  # Limit args length
                    'kwargs': str(kwargs)[:200]  # Limit kwargs length
                }
                handler.handle_error(e, context)
                raise
        return wrapper
    return decorator

def async_error_handler(component: str = None):
    """Async decorator for automatic error handling"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            handler = ErrorHandler(component or func.__module__)
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                context = {
                    'function': func.__name__,
                    'args': str(args)[:200],  # Limit args length
                    'kwargs': str(kwargs)[:200]  # Limit kwargs length
                }
                handler.handle_error(e, context)
                raise
        return wrapper
    return decorator

class MCPErrorHandler:
    """Specialized error handler for MCP operations"""

    def __init__(self):
        self.logger = logging.getLogger("mcp_error_handler")
        self.retry_count = 0
        self.max_retries = 3

    def handle_connection_error(self, error: Exception) -> bool:
        """Handle MCP connection errors with retry logic"""
        error_str = str(error).lower()
        self.logger.error(f"MCP connection failed: {error}")

        # Check for common issues
        if "eaddrinuse" in error_str:
            self.logger.info("Port already in use. Try stopping existing MCP server or use different port.")
            return False
        elif "connection refused" in error_str:
            self.logger.info("MCP server not running. Please start the MCP server first.")
            return False
        elif "timeout" in error_str:
            self.logger.info("MCP server timeout. Check server status and network connectivity.")
            return self.retry_count < self.max_retries
        elif "net::err_aborted" in error_str:
            self.logger.warning("Network request aborted - target may be unreachable or blocking requests")
            return self.retry_count < self.max_retries
        elif "execution context was destroyed" in error_str:
            self.logger.warning("Browser context destroyed - likely due to navigation or page reload")
            return self.retry_count < self.max_retries
        elif "ref not found" in error_str:
            self.logger.warning("Element reference lost - page may have changed")
            return self.retry_count < self.max_retries
        elif "no current snapshot available" in error_str:
            self.logger.warning("No browser snapshot available - need to navigate first")
            return True  # This is recoverable

        return False

    def handle_playwright_error(self, error: Exception, context: Dict[str, Any] = None) -> MCPError:
        """Handle Playwright-specific errors"""
        error_str = str(error).lower()
        details = context or {}

        if "net::err_aborted" in error_str:
            return MCPError(
                "Network request aborted - target may be unreachable or blocking automated requests",
                component="playwright",
                details={**details, "error_type": "network_aborted", "recoverable": True}
            )
        elif "execution context was destroyed" in error_str:
            return MCPError(
                "Browser execution context destroyed - page navigation interrupted",
                component="playwright",
                details={**details, "error_type": "context_destroyed", "recoverable": True}
            )
        elif "ref not found" in error_str:
            return MCPError(
                "Element reference not found - page structure may have changed",
                component="playwright",
                details={**details, "error_type": "element_not_found", "recoverable": True}
            )
        elif "no current snapshot available" in error_str:
            return MCPError(
                "No browser snapshot available - need to capture page state first",
                component="playwright",
                details={**details, "error_type": "no_snapshot", "recoverable": True}
            )
        else:
            return MCPError(
                f"Playwright error: {error}",
                component="playwright",
                details={**details, "error_type": "unknown", "recoverable": False}
            )

    def should_retry(self, error: Exception) -> bool:
        """Determine if an error is retryable"""
        error_str = str(error).lower()
        retryable_errors = [
            "timeout",
            "net::err_aborted",
            "execution context was destroyed",
            "ref not found",
            "connection reset",
            "network error"
        ]

        return any(err in error_str for err in retryable_errors) and self.retry_count < self.max_retries

    def increment_retry(self):
        """Increment retry counter"""
        self.retry_count += 1

    def reset_retry(self):
        """Reset retry counter"""
        self.retry_count = 0
    
    def handle_tool_error(self, tool_name: str, error: Exception) -> Optional[Dict[str, Any]]:
        """Handle MCP tool execution errors"""
        self.logger.error(f"MCP tool '{tool_name}' failed: {error}")
        
        # Return empty result for graceful degradation
        return {
            'error': True,
            'message': f"Tool {tool_name} failed: {str(error)}",
            'timestamp': datetime.now().isoformat()
        }

class LLMErrorHandler:
    """Specialized error handler for LLM operations"""
    
    def __init__(self):
        self.logger = logging.getLogger("llm_error_handler")
    
    def handle_api_error(self, provider: str, error: Exception) -> Optional[str]:
        """Handle LLM API errors"""
        error_str = str(error).lower()
        
        if "api key" in error_str or "unauthorized" in error_str:
            self.logger.error(f"{provider} API key invalid or missing")
            raise LLMError(f"Invalid API key for {provider}", component="llm")
        
        elif "rate limit" in error_str or "quota" in error_str:
            self.logger.warning(f"{provider} rate limit exceeded")
            raise LLMError(f"Rate limit exceeded for {provider}", component="llm")
        
        elif "timeout" in error_str:
            self.logger.warning(f"{provider} request timeout")
            raise LLMError(f"Request timeout for {provider}", component="llm")
        
        else:
            self.logger.error(f"Unexpected {provider} API error: {error}")
            raise LLMError(f"API error for {provider}: {str(error)}", component="llm")
    
    def handle_model_error(self, model: str, error: Exception) -> Optional[str]:
        """Handle model-specific errors"""
        self.logger.error(f"Model '{model}' error: {error}")
        
        if "not found" in str(error).lower():
            raise LLMError(f"Model '{model}' not found or not accessible", component="llm")
        
        return None

# Global error handlers
mcp_error_handler = MCPErrorHandler()
llm_error_handler = LLMErrorHandler()
