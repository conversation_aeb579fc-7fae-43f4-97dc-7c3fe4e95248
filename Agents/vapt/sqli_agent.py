"""
SQLi Agent - SQL Injection Vulnerability Testing
Combines SQLMap automation with AI-powered manual testing
"""

import asyncio
import json
import logging
import subprocess
import tempfile
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse, parse_qs

from config.settings import config
from config.llm_providers import LLMClientFactory
from utils.logging_utils import setup_component_logging, audit_logger, performance_monitor
from utils.error_handler import async_error_handler, SQLiAgentError

class SQLiAgent:
    """
    SQL Injection Testing Agent
    
    Capabilities:
    - Automated SQLMap scanning
    - AI-powered manual SQL injection testing
    - Raw request analysis and payload generation
    - Vulnerability validation and exploitation
    - Comprehensive reporting
    """
    
    def __init__(self):
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.logger = setup_component_logging(f"sqli_agent_{self.session_id}")
        self.llm_client = self._initialize_llm_client()
        
        # Results storage
        self.vulnerabilities = []
        self.sqlmap_results = {}
        self.manual_test_results = []
        
        self.logger.info("SQLi Agent initialized")
    
    def _initialize_llm_client(self):
        """Initialize LLM client"""
        try:
            return LLMClientFactory.create_client()
        except Exception as e:
            self.logger.error(f"Failed to initialize LLM client: {e}")
            return None
    
    @async_error_handler("sqli_agent")
    @performance_monitor
    async def execute(self, target_url: str) -> Dict[str, Any]:
        """Execute SQLi testing on target URL"""
        self.logger.info(f"Starting SQLi testing for {target_url}")
        audit_logger.log_scan_start(target_url, "sqli", self.session_id)
        
        start_time = datetime.now()
        
        try:
            # Step 1: Run SQLMap scan
            if config and config.sqli.enabled:
                await self._run_sqlmap_scan(target_url)
            
            # Step 2: AI-powered manual testing
            if self.llm_client:
                await self._run_manual_testing(target_url)
            
            # Step 3: Generate comprehensive report
            report = self._generate_report(target_url, start_time)
            
            duration = datetime.now() - start_time
            audit_logger.log_scan_complete(target_url, "sqli", self.session_id, str(duration))
            
            return report
            
        except Exception as e:
            self.logger.error(f"SQLi testing failed: {e}")
            raise SQLiAgentError(f"SQLi testing failed: {e}", "sqli_agent")
    
    @async_error_handler("sqli_agent")
    async def execute_from_report(self, preprocessor_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute SQLi testing using preprocessor report data"""
        target_url = preprocessor_data.get('metadata', {}).get('target_url', '')
        self.logger.info(f"Starting SQLi testing from report for {target_url}")
        
        start_time = datetime.now()
        
        try:
            # Extract raw requests from preprocessor data
            raw_requests = preprocessor_data.get('raw_requests', [])
            network_logs = preprocessor_data.get('network_logs', [])
            
            # Step 1: Analyze raw requests for SQL injection points
            injection_points = await self._analyze_injection_points(raw_requests, network_logs)
            
            # Step 2: Run targeted SQLMap scans
            if config and config.sqli.enabled and injection_points:
                await self._run_targeted_sqlmap_scans(injection_points)
            
            # Step 3: AI-powered manual testing on identified points
            if self.llm_client and injection_points:
                await self._run_targeted_manual_testing(injection_points)
            
            # Step 4: Generate comprehensive report
            report = self._generate_report(target_url, start_time)
            
            duration = datetime.now() - start_time
            audit_logger.log_scan_complete(target_url, "sqli_from_report", self.session_id, str(duration))
            
            return report
            
        except Exception as e:
            self.logger.error(f"SQLi testing from report failed: {e}")
            raise SQLiAgentError(f"SQLi testing from report failed: {e}", "sqli_agent")
    
    async def _run_sqlmap_scan(self, target_url: str):
        """Run SQLMap scan on target URL"""
        self.logger.info("Running SQLMap scan...")

        if not config or not Path(config.sqli.sqlmap_path).exists():
            self.logger.warning("SQLMap not found, skipping automated scan")
            return

        # Validate SQLMap installation
        try:
            # Test SQLMap with --version to ensure it's working
            test_process = await asyncio.create_subprocess_exec(
                config.sqli.sqlmap_path, "--version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await asyncio.wait_for(test_process.communicate(), timeout=10)

            if test_process.returncode != 0:
                self.logger.error(f"SQLMap validation failed: {stderr.decode()}")
                return

            self.logger.info(f"SQLMap version: {stdout.decode().strip()}")

        except Exception as e:
            self.logger.error(f"SQLMap validation error: {e}")
            return
        
        try:
            # Basic SQLMap command
            cmd = [
                config.sqli.sqlmap_path,
                "-u", target_url,
                "--batch",  # Non-interactive mode
                "--level", str(min(config.sqli.level, 3)),  # Limit level for faster scanning
                "--risk", str(min(config.sqli.risk, 2)),   # Limit risk for safer scanning
                "--timeout", str(min(config.sqli.timeout, 120)),  # Limit timeout
                "--output-dir", str(Path(config.general.reports_dir) / "sqlmap"),
                "--threads", "2",  # Use 2 threads for faster scanning
                "--technique", "BEUST"  # All techniques: Boolean, Error, Union, Stacked, Time
            ]
            
            # Run SQLMap
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=config.sqli.timeout
            )
            
            # Process results
            stdout_text = stdout.decode() if stdout else ""
            stderr_text = stderr.decode() if stderr else ""

            if process.returncode == 0:
                self.logger.info("SQLMap scan completed successfully")
                self._process_sqlmap_results(stdout_text, stderr_text)
            else:
                self.logger.error(f"SQLMap failed with return code {process.returncode}")
                self.logger.error(f"SQLMap stderr: {stderr_text[:500]}")  # Limit error message length

                # Check for common SQLMap errors
                if "no such option" in stderr_text:
                    self.logger.error("SQLMap command line option error - check SQLMap version compatibility")
                elif "connection" in stderr_text.lower():
                    self.logger.error("SQLMap connection error - target may be unreachable")
                elif "permission" in stderr_text.lower():
                    self.logger.error("SQLMap permission error - check file system permissions")

        except asyncio.TimeoutError:
            self.logger.error(f"SQLMap scan timed out after {config.sqli.timeout} seconds")
        except Exception as e:
            self.logger.error(f"SQLMap scan error: {e}")
            import traceback
            self.logger.debug(f"SQLMap error traceback: {traceback.format_exc()}")
    
    def _process_sqlmap_results(self, stdout: str, stderr: str):
        """Process SQLMap results"""
        self.logger.info("Processing SQLMap results...")
        
        try:
            # Parse SQLMap output for vulnerabilities
            if "sqlmap identified the following injection point" in stdout.lower():
                # Extract injection points and vulnerabilities
                vulnerabilities = self._extract_sqlmap_vulnerabilities(stdout)
                self.vulnerabilities.extend(vulnerabilities)
                
                for vuln in vulnerabilities:
                    audit_logger.log_vulnerability_found(
                        "SQL Injection",
                        vuln.get('severity', 'medium'),
                        vuln.get('url', ''),
                        vuln
                    )
            
            self.sqlmap_results = {
                'stdout': stdout,
                'stderr': stderr,
                'vulnerabilities_found': len(self.vulnerabilities),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error processing SQLMap results: {e}")
    
    def _extract_sqlmap_vulnerabilities(self, output: str) -> List[Dict[str, Any]]:
        """Extract vulnerabilities from SQLMap output"""
        vulnerabilities = []
        
        try:
            # Parse SQLMap output for injection points
            injection_pattern = r"Parameter: (\w+) \((\w+)\).*?Type: (\w+).*?Title: ([^\n]+)"
            matches = re.findall(injection_pattern, output, re.DOTALL | re.IGNORECASE)
            
            for match in matches:
                parameter, location, injection_type, title = match
                
                vulnerability = {
                    'type': 'SQL Injection',
                    'severity': self._determine_severity(injection_type),
                    'parameter': parameter,
                    'location': location,
                    'injection_type': injection_type,
                    'title': title.strip(),
                    'tool': 'SQLMap',
                    'timestamp': datetime.now().isoformat(),
                    'status': 'confirmed'
                }
                
                vulnerabilities.append(vulnerability)
                
        except Exception as e:
            self.logger.error(f"Error extracting SQLMap vulnerabilities: {e}")
        
        return vulnerabilities
    
    def _determine_severity(self, injection_type: str) -> str:
        """Determine vulnerability severity based on injection type"""
        high_risk_types = ['boolean-based blind', 'error-based', 'union query']
        medium_risk_types = ['time-based blind', 'stacked queries']
        
        injection_type_lower = injection_type.lower()
        
        if any(risk_type in injection_type_lower for risk_type in high_risk_types):
            return 'high'
        elif any(risk_type in injection_type_lower for risk_type in medium_risk_types):
            return 'medium'
        else:
            return 'low'
    
    async def _run_manual_testing(self, target_url: str):
        """Run AI-powered manual SQL injection testing"""
        if not self.llm_client:
            self.logger.warning("No LLM client available for manual testing")
            return
        
        self.logger.info("Running AI-powered manual SQL injection testing...")
        
        try:
            # Generate test payloads using AI
            payloads = await self._generate_ai_payloads(target_url)
            
            # Test each payload
            for payload in payloads:
                result = await self._test_payload(target_url, payload)
                if result:
                    self.manual_test_results.append(result)
                    
                    # If vulnerability found, add to main list
                    if result.get('vulnerable', False):
                        vulnerability = {
                            'type': 'SQL Injection',
                            'severity': result.get('severity', 'medium'),
                            'payload': payload,
                            'response': result.get('response', ''),
                            'tool': 'AI Manual Testing',
                            'timestamp': datetime.now().isoformat(),
                            'status': 'potential'
                        }
                        
                        self.vulnerabilities.append(vulnerability)
                        
                        audit_logger.log_vulnerability_found(
                            "SQL Injection",
                            vulnerability['severity'],
                            target_url,
                            vulnerability
                        )
                        
        except Exception as e:
            self.logger.error(f"Manual testing failed: {e}")
    
    async def _generate_ai_payloads(self, target_url: str) -> List[str]:
        """Generate SQL injection payloads using AI"""
        if not self.llm_client:
            return []
        
        prompt = f"""
Generate a list of SQL injection payloads for testing the URL: {target_url}

Consider the following:
1. Different SQL injection types (Union, Boolean, Time-based, Error-based)
2. Various database systems (MySQL, PostgreSQL, SQLite, MSSQL, Oracle)
3. Different parameter locations (GET, POST, Headers, Cookies)
4. Bypass techniques for common filters

Return ONLY a JSON array of payload strings, no other text:
["payload1", "payload2", ...]

Focus on effective, commonly successful payloads.
"""
        
        try:
            response = LLMClientFactory.generate_completion(
                self.llm_client,
                prompt,
                temperature=0.7,
                max_tokens=1000
            )
            
            if response:
                # Extract JSON array from response
                import ast
                match = re.search(r'\[.*\]', response, re.DOTALL)
                if match:
                    payloads = ast.literal_eval(match.group(0))
                    return [str(p) for p in payloads if isinstance(p, str)]
            
            return []
            
        except Exception as e:
            self.logger.error(f"Failed to generate AI payloads: {e}")
            return []
    
    async def _test_payload(self, target_url: str, payload: str) -> Optional[Dict[str, Any]]:
        """Test a single SQL injection payload"""
        try:
            import aiohttp
            
            # Test payload in URL parameter
            test_url = f"{target_url}?id={payload}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(test_url, timeout=10) as response:
                    response_text = await response.text()
                    
                    # Analyze response for SQL injection indicators
                    is_vulnerable = self._analyze_response_for_sqli(response_text, response.status)
                    
                    return {
                        'payload': payload,
                        'url': test_url,
                        'status_code': response.status,
                        'response': response_text[:1000],  # Limit response size
                        'vulnerable': is_vulnerable,
                        'severity': 'medium' if is_vulnerable else 'info',
                        'timestamp': datetime.now().isoformat()
                    }
                    
        except Exception as e:
            self.logger.error(f"Error testing payload {payload}: {e}")
            return None
    
    def _analyze_response_for_sqli(self, response_text: str, status_code: int) -> bool:
        """Analyze HTTP response for SQL injection indicators"""
        # SQL error patterns
        sql_errors = [
            r"sql syntax.*mysql",
            r"warning.*mysql_",
            r"valid mysql result",
            r"postgresql.*error",
            r"warning.*pg_",
            r"valid postgresql result",
            r"sqlite_exception",
            r"sqlite error",
            r"microsoft.*odbc.*sql server",
            r"sqlserver jdbc",
            r"oracle error",
            r"oracle.*driver",
            r"warning.*oci_"
        ]
        
        response_lower = response_text.lower()
        
        # Check for SQL error messages
        for pattern in sql_errors:
            if re.search(pattern, response_lower, re.IGNORECASE):
                return True
        
        # Check for other indicators
        if status_code == 500:  # Internal server error might indicate SQL error
            return True
        
        return False

    async def _analyze_injection_points(self, raw_requests: List[str], network_logs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Analyze raw requests and network logs for potential injection points"""
        injection_points = []

        try:
            # Analyze raw requests
            for request in raw_requests:
                points = self._extract_injection_points_from_request(request)
                injection_points.extend(points)

            # Analyze network logs
            for log in network_logs:
                points = self._extract_injection_points_from_network_log(log)
                injection_points.extend(points)

            self.logger.info(f"Identified {len(injection_points)} potential injection points")
            return injection_points

        except Exception as e:
            self.logger.error(f"Error analyzing injection points: {e}")
            return []

    def _extract_injection_points_from_request(self, raw_request: str) -> List[Dict[str, Any]]:
        """Extract potential injection points from raw HTTP request"""
        points = []

        try:
            lines = raw_request.split('\n')
            if not lines:
                return points

            # Parse request line
            request_line = lines[0].strip()
            method, path, _ = request_line.split(' ', 2)

            # Extract URL parameters
            if '?' in path:
                url_part, query_part = path.split('?', 1)
                params = parse_qs(query_part)

                for param_name, param_values in params.items():
                    points.append({
                        'type': 'url_parameter',
                        'method': method,
                        'path': url_part,
                        'parameter': param_name,
                        'value': param_values[0] if param_values else '',
                        'location': 'GET'
                    })

            # Extract POST data if present
            if method.upper() == 'POST':
                # Find body (after empty line)
                body_start = -1
                for i, line in enumerate(lines):
                    if line.strip() == '':
                        body_start = i + 1
                        break

                if body_start > 0 and body_start < len(lines):
                    body = '\n'.join(lines[body_start:])
                    if body.strip():
                        # Try to parse as form data
                        if 'application/x-www-form-urlencoded' in raw_request:
                            params = parse_qs(body)
                            for param_name, param_values in params.items():
                                points.append({
                                    'type': 'post_parameter',
                                    'method': method,
                                    'path': path.split('?')[0],
                                    'parameter': param_name,
                                    'value': param_values[0] if param_values else '',
                                    'location': 'POST'
                                })
                        else:
                            # JSON or other body types
                            points.append({
                                'type': 'request_body',
                                'method': method,
                                'path': path.split('?')[0],
                                'parameter': 'body',
                                'value': body[:200],  # Limit body size
                                'location': 'BODY'
                            })

        except Exception as e:
            self.logger.error(f"Error extracting injection points from request: {e}")

        return points

    def _extract_injection_points_from_network_log(self, network_log: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract potential injection points from network log entry with enhanced analysis"""
        points = []

        try:
            url = network_log.get('url', '')
            method = network_log.get('method', 'GET')
            headers = network_log.get('requestHeaders', network_log.get('headers', {}))

            if not url:
                return points

            parsed_url = urlparse(url)

            # Skip static resources
            static_extensions = ['.js', '.css', '.png', '.jpg', '.gif', '.ico', '.woff', '.ttf', '.svg']
            if any(parsed_url.path.lower().endswith(ext) for ext in static_extensions):
                return points

            # Focus on API endpoints and dynamic pages
            is_api_endpoint = any(api_path in parsed_url.path.lower() for api_path in ['/api/', '/rest/', '/login', '/search', '/user', '/admin'])
            is_dynamic_page = parsed_url.query or method.upper() in ['POST', 'PUT', 'PATCH']

            if not (is_api_endpoint or is_dynamic_page):
                return points

            # Extract query parameters
            if parsed_url.query:
                params = parse_qs(parsed_url.query)
                for param_name, param_values in params.items():
                    # Prioritize parameters that are commonly vulnerable
                    priority = 'high' if any(keyword in param_name.lower() for keyword in ['id', 'search', 'q', 'query', 'user', 'email']) else 'medium'

                    points.append({
                        'type': 'url_parameter',
                        'method': method,
                        'url': url,
                        'parameter': param_name,
                        'value': param_values[0] if param_values else '',
                        'location': 'GET',
                        'headers': headers,
                        'priority': priority
                    })

            # Extract POST data
            post_data = network_log.get('postData', '')
            if post_data and method.upper() in ['POST', 'PUT', 'PATCH']:
                try:
                    # Try to parse as JSON
                    json_data = json.loads(post_data)
                    if isinstance(json_data, dict):
                        for key, value in json_data.items():
                            # High priority for authentication and user-related fields
                            priority = 'high' if any(keyword in key.lower() for keyword in ['email', 'username', 'password', 'id', 'user_id', 'login']) else 'medium'

                            points.append({
                                'type': 'json_parameter',
                                'method': method,
                                'url': url,
                                'parameter': key,
                                'value': str(value),
                                'location': 'JSON',
                                'headers': headers,
                                'priority': priority,
                                'content_type': 'application/json'
                            })
                except json.JSONDecodeError:
                    # Try to parse as form data
                    params = parse_qs(post_data)
                    for param_name, param_values in params.items():
                        priority = 'high' if any(keyword in param_name.lower() for keyword in ['email', 'username', 'password', 'id', 'search', 'query']) else 'medium'

                        points.append({
                            'type': 'post_parameter',
                            'method': method,
                            'url': url,
                            'parameter': param_name,
                            'value': param_values[0] if param_values else '',
                            'location': 'POST',
                            'headers': headers,
                            'priority': priority,
                            'content_type': 'application/x-www-form-urlencoded'
                        })

            # Add URL path parameters (for REST APIs like /api/users/123)
            path_segments = [seg for seg in parsed_url.path.split('/') if seg]
            if len(path_segments) > 1 and path_segments[-1].isdigit():
                points.append({
                    'type': 'path_parameter',
                    'method': method,
                    'url': url,
                    'parameter': 'id',
                    'value': path_segments[-1],
                    'location': 'PATH',
                    'headers': headers,
                    'priority': 'high'
                })

        except Exception as e:
            self.logger.error(f"Error extracting injection points from network log: {e}")

        return points

    async def _run_targeted_sqlmap_scans(self, injection_points: List[Dict[str, Any]]):
        """Run targeted SQLMap scans on identified injection points"""
        if not config or not Path(config.sqli.sqlmap_path).exists():
            self.logger.warning("SQLMap not available, skipping automated scans")
            return

        self.logger.info(f"Running targeted SQLMap scans on {len(injection_points)} injection points...")

        # Group injection points by URL and method for efficient scanning
        url_groups = {}
        for point in injection_points[:10]:  # Limit to first 10 points
            url = point.get('url', '')
            method = point.get('method', 'GET')
            key = f"{method}:{url}"

            if key not in url_groups:
                url_groups[key] = []
            url_groups[key].append(point)

        # Run SQLMap on each unique URL/method combination
        for url_key, points in url_groups.items():
            try:
                method, url = url_key.split(':', 1)
                await self._run_sqlmap_on_url_group(url, method, points)
            except Exception as e:
                self.logger.error(f"Error running SQLMap on URL group {url_key}: {e}")

    async def _run_sqlmap_on_url_group(self, url: str, method: str, injection_points: List[Dict[str, Any]]):
        """Run SQLMap on a URL with multiple injection points"""
        try:
            if not url:
                return

            # Build SQLMap command based on method
            cmd = [
                config.sqli.sqlmap_path,
                "-u", url,
                "--batch",
                "--level", str(min(config.sqli.level, 3)),  # Reduce level for faster scanning
                "--risk", str(min(config.sqli.risk, 2)),   # Reduce risk for safer scanning
                "--timeout", "30",  # Shorter timeout for targeted scans
                "--threads", "2",
                "--technique", "BEUST",  # All techniques
                "--tamper", "space2comment"  # Basic tamper script
            ]

            # Add method-specific options
            if method.upper() == 'POST':
                # For POST requests, we need to provide data
                post_data = self._build_post_data_from_injection_points(injection_points)
                if post_data:
                    cmd.extend(["--data", post_data])

                # Test specific parameters if available
                parameters = [point.get('parameter', '') for point in injection_points if point.get('parameter')]
                if parameters:
                    cmd.extend(["-p", ",".join(parameters[:3])])  # Limit to 3 parameters

            else:  # GET method
                # Test specific parameters
                parameters = [point.get('parameter', '') for point in injection_points if point.get('parameter')]
                if parameters:
                    cmd.extend(["-p", ",".join(parameters[:3])])  # Limit to 3 parameters

            # Add headers if available
            headers = self._extract_headers_from_injection_points(injection_points)
            if headers:
                for header_name, header_value in headers.items():
                    cmd.extend(["--header", f"{header_name}: {header_value}"])

            self.logger.info(f"Running SQLMap on {url} with method {method}")

            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=90  # 1.5 minute timeout for URL group scans
            )

            if process.returncode == 0:
                self._process_sqlmap_results(stdout.decode(), stderr.decode())
            else:
                self.logger.warning(f"SQLMap scan failed for {url}: {stderr.decode()[:200]}")

        except asyncio.TimeoutError:
            self.logger.warning(f"SQLMap scan timed out for {url}")
        except Exception as e:
            self.logger.error(f"Error in SQLMap URL group scan: {e}")

    def _build_post_data_from_injection_points(self, injection_points: List[Dict[str, Any]]) -> str:
        """Build POST data string from injection points"""
        post_data_parts = []

        for point in injection_points:
            if point.get('location') in ['POST', 'JSON']:
                parameter = point.get('parameter', '')
                value = point.get('value', 'test')

                if parameter:
                    if point.get('location') == 'JSON':
                        # For JSON, we'll use a simple key-value format
                        post_data_parts.append(f'"{parameter}":"{value}"')
                    else:
                        # For form data
                        post_data_parts.append(f"{parameter}={value}")

        if post_data_parts:
            if any('JSON' in point.get('location', '') for point in injection_points):
                return "{" + ",".join(post_data_parts) + "}"
            else:
                return "&".join(post_data_parts)

        return "test=1"  # Default POST data

    def _extract_headers_from_injection_points(self, injection_points: List[Dict[str, Any]]) -> Dict[str, str]:
        """Extract common headers from injection points"""
        headers = {}

        for point in injection_points:
            if 'headers' in point:
                point_headers = point['headers']
                if isinstance(point_headers, dict):
                    # Add common headers
                    for header_name in ['User-Agent', 'Content-Type', 'Authorization', 'X-Requested-With']:
                        if header_name.lower() in [h.lower() for h in point_headers.keys()]:
                            headers[header_name] = point_headers.get(header_name, '')

        # Add default headers if not present
        if 'User-Agent' not in headers:
            headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'

        return headers

    async def _run_targeted_manual_testing(self, injection_points: List[Dict[str, Any]]):
        """Run AI-powered manual testing on specific injection points"""
        if not self.llm_client:
            return

        self.logger.info(f"Running targeted manual testing on {len(injection_points)} injection points...")

        for point in injection_points:
            try:
                payloads = await self._generate_targeted_payloads(point)
                for payload in payloads:
                    result = await self._test_targeted_payload(point, payload)
                    if result:
                        self.manual_test_results.append(result)

                        if result.get('vulnerable', False):
                            vulnerability = {
                                'type': 'SQL Injection',
                                'severity': result.get('severity', 'medium'),
                                'injection_point': point,
                                'payload': payload,
                                'response': result.get('response', ''),
                                'tool': 'AI Targeted Testing',
                                'timestamp': datetime.now().isoformat(),
                                'status': 'potential'
                            }

                            self.vulnerabilities.append(vulnerability)

            except Exception as e:
                self.logger.error(f"Error in targeted manual testing: {e}")

    async def _generate_targeted_payloads(self, injection_point: Dict[str, Any]) -> List[str]:
        """Generate targeted payloads for specific injection point"""
        if not self.llm_client:
            return []

        point_info = json.dumps(injection_point, indent=2)

        prompt = f"""
Generate targeted SQL injection payloads for this specific injection point:

{point_info}

Consider:
1. The parameter type and location
2. The HTTP method
3. The parameter value format
4. Common bypass techniques for this context

Return ONLY a JSON array of 5-10 targeted payload strings:
["payload1", "payload2", ...]
"""

        try:
            response = LLMClientFactory.generate_completion(
                self.llm_client,
                prompt,
                temperature=0.5,
                max_tokens=500
            )

            if response:
                import ast
                match = re.search(r'\[.*\]', response, re.DOTALL)
                if match:
                    payloads = ast.literal_eval(match.group(0))
                    return [str(p) for p in payloads if isinstance(p, str)]

            return []

        except Exception as e:
            self.logger.error(f"Failed to generate targeted payloads: {e}")
            return []

    async def _test_targeted_payload(self, injection_point: Dict[str, Any], payload: str) -> Optional[Dict[str, Any]]:
        """Test payload on specific injection point"""
        try:
            import aiohttp

            url = injection_point.get('url', '')
            parameter = injection_point.get('parameter', '')
            method = injection_point.get('method', 'GET').upper()
            location = injection_point.get('location', 'GET')

            if not url or not parameter:
                return None

            async with aiohttp.ClientSession() as session:
                if location == 'GET':
                    # Modify URL parameter
                    parsed_url = urlparse(url)
                    params = parse_qs(parsed_url.query) if parsed_url.query else {}
                    params[parameter] = [payload]

                    from urllib.parse import urlencode
                    new_query = urlencode(params, doseq=True)
                    test_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}?{new_query}"

                    async with session.get(test_url, timeout=10) as response:
                        response_text = await response.text()
                        is_vulnerable = self._analyze_response_for_sqli(response_text, response.status)

                        return {
                            'injection_point': injection_point,
                            'payload': payload,
                            'url': test_url,
                            'status_code': response.status,
                            'response': response_text[:500],
                            'vulnerable': is_vulnerable,
                            'severity': 'medium' if is_vulnerable else 'info',
                            'timestamp': datetime.now().isoformat()
                        }

                elif location in ['POST', 'JSON']:
                    # Modify POST data
                    if location == 'JSON':
                        data = {parameter: payload}
                        headers = {'Content-Type': 'application/json'}
                        async with session.post(url, json=data, headers=headers, timeout=10) as response:
                            response_text = await response.text()
                            is_vulnerable = self._analyze_response_for_sqli(response_text, response.status)

                            return {
                                'injection_point': injection_point,
                                'payload': payload,
                                'url': url,
                                'status_code': response.status,
                                'response': response_text[:500],
                                'vulnerable': is_vulnerable,
                                'severity': 'medium' if is_vulnerable else 'info',
                                'timestamp': datetime.now().isoformat()
                            }
                    else:
                        data = {parameter: payload}
                        async with session.post(url, data=data, timeout=10) as response:
                            response_text = await response.text()
                            is_vulnerable = self._analyze_response_for_sqli(response_text, response.status)

                            return {
                                'injection_point': injection_point,
                                'payload': payload,
                                'url': url,
                                'status_code': response.status,
                                'response': response_text[:500],
                                'vulnerable': is_vulnerable,
                                'severity': 'medium' if is_vulnerable else 'info',
                                'timestamp': datetime.now().isoformat()
                            }

        except Exception as e:
            self.logger.error(f"Error testing targeted payload: {e}")
            return None

    def _generate_report(self, target_url: str, start_time: datetime) -> Dict[str, Any]:
        """Generate comprehensive SQLi testing report"""
        end_time = datetime.now()

        report = {
            'metadata': {
                'target_url': target_url,
                'scan_type': 'SQL Injection',
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': str(end_time - start_time),
                'session_id': self.session_id
            },
            'summary': {
                'total_vulnerabilities': len(self.vulnerabilities),
                'confirmed_vulnerabilities': len([v for v in self.vulnerabilities if v.get('status') == 'confirmed']),
                'potential_vulnerabilities': len([v for v in self.vulnerabilities if v.get('status') == 'potential']),
                'sqlmap_enabled': config.sqli.enabled if config else False,
                'manual_testing_enabled': self.llm_client is not None
            },
            'vulnerabilities': self.vulnerabilities,
            'sqlmap_results': self.sqlmap_results,
            'manual_test_results': self.manual_test_results,
            'recommendations': self._generate_recommendations()
        }

        return report

    def _generate_recommendations(self) -> List[str]:
        """Generate security recommendations based on findings"""
        recommendations = [
            "Use parameterized queries (prepared statements) for all database interactions",
            "Implement proper input validation and sanitization",
            "Apply the principle of least privilege for database accounts",
            "Enable database query logging and monitoring",
            "Regularly update database software and apply security patches"
        ]

        if self.vulnerabilities:
            recommendations.extend([
                "Immediately patch identified SQL injection vulnerabilities",
                "Conduct a comprehensive code review of database interaction code",
                "Implement Web Application Firewall (WAF) rules to block SQL injection attempts",
                "Consider using an ORM (Object-Relational Mapping) framework"
            ])

        return recommendations
