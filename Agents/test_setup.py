#!/usr/bin/env python3
"""
VAPT AI Tool - Setup Test Script
Quick test to verify the installation and configuration
"""

import sys
import asyncio
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test if all modules can be imported"""
    print("🧪 Testing module imports...")
    
    try:
        from config.settings import config
        print("✅ Config module imported")
    except Exception as e:
        print(f"❌ Config import failed: {e}")
        return False
    
    try:
        from config.llm_providers import LLMClientFactory, AgnoModelFactory
        print("✅ LLM providers module imported")
    except Exception as e:
        print(f"❌ LLM providers import failed: {e}")
        return False
    
    try:
        from utils.logging_utils import setup_component_logging
        print("✅ Logging utils imported")
    except Exception as e:
        print(f"❌ Logging utils import failed: {e}")
        return False
    
    try:
        from utils.error_handler import VAPTError
        print("✅ Error handler imported")
    except Exception as e:
        print(f"❌ Error handler import failed: {e}")
        return False
    
    try:
        from lead.lead_agent import LeadAgent
        print("✅ Lead agent imported")
    except Exception as e:
        print(f"❌ Lead agent import failed: {e}")
        return False
    
    try:
        from preprocessor.browser_agent import BrowserAgent
        print("✅ Browser agent imported")
    except Exception as e:
        print(f"❌ Browser agent import failed: {e}")
        return False
    
    try:
        from vapt.sqli_agent import SQLiAgent
        print("✅ SQLi agent imported")
    except Exception as e:
        print(f"❌ SQLi agent import failed: {e}")
        return False
    
    try:
        from vapt.xss_agent import XSSAgent
        print("✅ XSS agent imported")
    except Exception as e:
        print(f"❌ XSS agent import failed: {e}")
        return False
    
    return True

def test_configuration():
    """Test configuration loading"""
    print("\n🔧 Testing configuration...")
    
    try:
        from config.settings import config
        
        if not config:
            print("❌ Configuration not loaded")
            return False
        
        print(f"✅ LLM Provider: {config.llm.provider}")
        print(f"✅ LLM Model: {config.llm.model}")
        print(f"✅ Reports Dir: {config.general.reports_dir}")
        print(f"✅ Logs Dir: {config.general.logs_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_llm_client():
    """Test LLM client creation"""
    print("\n🤖 Testing LLM client...")
    
    try:
        from config.llm_providers import LLMClientFactory
        
        client = LLMClientFactory.create_client()
        if client:
            print("✅ LLM client created successfully")
            return True
        else:
            print("❌ LLM client creation returned None")
            return False
            
    except Exception as e:
        print(f"❌ LLM client test failed: {e}")
        return False

def test_agno_model():
    """Test Agno model creation"""
    print("\n🎭 Testing Agno model...")
    
    try:
        from config.llm_providers import AgnoModelFactory
        
        model = AgnoModelFactory.create_agno_model()
        if model:
            print("✅ Agno model created successfully")
            return True
        else:
            print("❌ Agno model creation returned None")
            return False
            
    except Exception as e:
        print(f"❌ Agno model test failed: {e}")
        return False

def test_directories():
    """Test directory creation"""
    print("\n📁 Testing directories...")
    
    try:
        from config.settings import config
        
        if not config:
            print("❌ Configuration not available")
            return False
        
        reports_dir = Path(config.general.reports_dir)
        logs_dir = Path(config.general.logs_dir)
        
        # Create directories if they don't exist
        reports_dir.mkdir(exist_ok=True)
        logs_dir.mkdir(exist_ok=True)
        
        if reports_dir.exists():
            print(f"✅ Reports directory exists: {reports_dir}")
        else:
            print(f"❌ Reports directory missing: {reports_dir}")
            return False
        
        if logs_dir.exists():
            print(f"✅ Logs directory exists: {logs_dir}")
        else:
            print(f"❌ Logs directory missing: {logs_dir}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Directory test failed: {e}")
        return False

def test_logging():
    """Test logging setup"""
    print("\n📝 Testing logging...")
    
    try:
        from utils.logging_utils import setup_component_logging
        
        logger = setup_component_logging("test_setup")
        logger.info("Test log message")
        
        print("✅ Logging setup successful")
        return True
        
    except Exception as e:
        print(f"❌ Logging test failed: {e}")
        return False

async def test_lead_agent():
    """Test Lead Agent initialization"""
    print("\n🧠 Testing Lead Agent...")
    
    try:
        from lead.lead_agent import LeadAgent
        
        lead_agent = LeadAgent()
        if lead_agent:
            print("✅ Lead Agent initialized successfully")
            print(f"✅ Session ID: {lead_agent.session_id}")
            return True
        else:
            print("❌ Lead Agent initialization failed")
            return False
            
    except Exception as e:
        print(f"❌ Lead Agent test failed: {e}")
        return False

def test_playwright_mcp():
    """Test Playwright MCP availability"""
    print("\n🎭 Testing Playwright MCP...")
    
    # Check if playwright-mcp directory exists
    playwright_paths = [
        Path("../playwright-mcp/cli.js"),
        Path("../../playwright-mcp/cli.js"),
        Path("playwright-mcp/cli.js")
    ]
    
    for path in playwright_paths:
        if path.exists():
            print(f"✅ Playwright MCP found: {path}")
            return True
    
    print("⚠️  Playwright MCP not found")
    print("   Run: git clone https://github.com/CurlSek/playwright-mcp")
    print("   Then: cd playwright-mcp && npm install && npm run build")
    return False

async def main():
    """Main test function"""
    print("🧪 VAPT AI Tool - Setup Test")
    print("="*40)
    
    tests = [
        ("Module Imports", test_imports),
        ("Configuration", test_configuration),
        ("LLM Client", test_llm_client),
        ("Agno Model", test_agno_model),
        ("Directories", test_directories),
        ("Logging", test_logging),
        ("Lead Agent", test_lead_agent),
        ("Playwright MCP", test_playwright_mcp)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
            else:
                failed += 1
                
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            failed += 1
    
    # Summary
    print("\n" + "="*40)
    print("📊 Test Results Summary")
    print("="*40)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Total: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 All tests passed! VAPT AI Tool is ready to use.")
        print("\nNext steps:")
        print("1. python main.py --config-validate")
        print("2. python main.py https://juice-shop.herokuapp.com --vulns preprocessor")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please resolve issues before using the tool.")
        print("\nTroubleshooting:")
        print("1. Check your .env file configuration")
        print("2. Ensure all dependencies are installed: pip install -r requirements.txt")
        print("3. Setup Playwright MCP: git clone https://github.com/CurlSek/playwright-mcp")
    
    print("="*40)
    
    return failed == 0

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
