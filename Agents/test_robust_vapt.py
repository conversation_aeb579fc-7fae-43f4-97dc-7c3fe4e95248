#!/usr/bin/env python3
"""
Comprehensive VAPT Tool Robustness Test
Tests all components with error handling and fallback mechanisms
"""

import sys
import asyncio
import json
from pathlib import Path
from datetime import datetime

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from preprocessor.browser_agent import BrowserAgent
from preprocessor.fallback_browser import FallbackBrowserAgent
from vapt.sqli_agent import SQLiAgent
from vapt.xss_agent import XSSAgent
from vapt.lead_agent import LeadAgent
from utils.error_handler import MCP<PERSON>rrorHandler
from config.settings import config


class VAPTRobustnessTest:
    """Comprehensive test suite for VAPT tool robustness"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
        
    async def run_all_tests(self):
        """Run all robustness tests"""
        print("🚀 VAPT Tool Robustness Test Suite")
        print("="*60)
        
        tests = [
            ("MCP Error Handling", self.test_mcp_error_handling),
            ("Fallback Browser Agent", self.test_fallback_browser),
            ("SQLi Agent Robustness", self.test_sqli_robustness),
            ("XSS Agent Robustness", self.test_xss_robustness),
            ("Lead Agent Orchestration", self.test_lead_agent),
            ("End-to-End Resilience", self.test_end_to_end)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            print(f"\n🧪 Running: {test_name}")
            print("-" * 40)
            
            try:
                result = await test_func()
                if result:
                    print(f"✅ {test_name}: PASSED")
                    passed += 1
                else:
                    print(f"❌ {test_name}: FAILED")
                    failed += 1
                self.test_results[test_name] = result
                
            except Exception as e:
                print(f"💥 {test_name}: CRASHED - {e}")
                failed += 1
                self.test_results[test_name] = False
        
        # Print summary
        print("\n" + "="*60)
        print("📊 Test Results Summary")
        print("="*60)
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        print(f"📊 Total: {passed + failed}")
        print(f"⏱️  Duration: {datetime.now() - self.start_time}")
        
        if failed == 0:
            print("\n🎉 All tests passed! VAPT tool is robust and ready for production.")
        else:
            print(f"\n⚠️  {failed} test(s) failed. Review the issues above.")
        
        return failed == 0
    
    async def test_mcp_error_handling(self):
        """Test MCP error handling and recovery"""
        try:
            error_handler = MCPErrorHandler()
            
            # Test various error scenarios
            test_errors = [
                Exception("net::ERR_ABORTED"),
                Exception("Execution context was destroyed"),
                Exception("ref not found"),
                Exception("No current snapshot available"),
                Exception("Connection refused"),
                Exception("timeout")
            ]
            
            recoverable_count = 0
            for error in test_errors:
                if error_handler.should_retry(error):
                    recoverable_count += 1
                
                # Test error handling
                mcp_error = error_handler.handle_playwright_error(error)
                if mcp_error.details.get("recoverable"):
                    recoverable_count += 1
            
            print(f"   - Tested {len(test_errors)} error scenarios")
            print(f"   - {recoverable_count} errors marked as recoverable")
            print(f"   - Error handler working correctly")
            
            return recoverable_count > 0
            
        except Exception as e:
            print(f"   - MCP error handling test failed: {e}")
            return False
    
    async def test_fallback_browser(self):
        """Test fallback browser agent"""
        try:
            # Test with a reliable target
            test_url = "https://httpbin.org/html"
            fallback_agent = FallbackBrowserAgent(test_url)
            
            print(f"   - Testing fallback agent with {test_url}")
            
            # Execute fallback reconnaissance
            result = await asyncio.wait_for(fallback_agent.execute(), timeout=60)
            
            # Validate results
            if not result:
                print("   - No result returned")
                return False
            
            # Check required fields
            required_fields = ["metadata", "recon", "network_logs", "raw_requests"]
            missing_fields = [field for field in required_fields if field not in result]
            
            if missing_fields:
                print(f"   - Missing required fields: {missing_fields}")
                return False
            
            # Check if we got some data
            network_count = len(result.get("network_logs", []))
            recon_count = len(result.get("recon", []))
            raw_count = len(result.get("raw_requests", []))
            
            print(f"   - Network logs: {network_count}")
            print(f"   - Recon tasks: {recon_count}")
            print(f"   - Raw requests: {raw_count}")
            
            return network_count > 0 and recon_count > 0
            
        except Exception as e:
            print(f"   - Fallback browser test failed: {e}")
            return False
    
    async def test_sqli_robustness(self):
        """Test SQLi agent robustness"""
        try:
            sqli_agent = SQLiAgent()
            
            # Test with mock network logs
            mock_network_logs = [
                {
                    "url": "https://example.com/login",
                    "method": "POST",
                    "postData": '{"email":"<EMAIL>","password":"test123"}',
                    "headers": {"Content-Type": "application/json"}
                },
                {
                    "url": "https://example.com/search?q=test",
                    "method": "GET",
                    "headers": {"Accept": "application/json"}
                }
            ]
            
            mock_raw_requests = [
                "POST /login HTTP/1.1\r\nHost: example.com\r\nContent-Type: application/json\r\n\r\n{\"email\":\"test\",\"password\":\"test\"}"
            ]
            
            print("   - Testing injection point analysis...")
            injection_points = await sqli_agent._analyze_injection_points(mock_raw_requests, mock_network_logs)
            
            print(f"   - Found {len(injection_points)} injection points")
            
            if len(injection_points) > 0:
                # Test payload generation
                print("   - Testing payload generation...")
                payloads = await sqli_agent._generate_targeted_payloads(injection_points[0])
                print(f"   - Generated {len(payloads)} payloads")
                
                return len(payloads) > 0
            
            return len(injection_points) > 0
            
        except Exception as e:
            print(f"   - SQLi robustness test failed: {e}")
            return False
    
    async def test_xss_robustness(self):
        """Test XSS agent robustness"""
        try:
            xss_agent = XSSAgent()
            
            # Test with mock data
            mock_raw_requests = [
                "GET /search?q=test HTTP/1.1\r\nHost: example.com\r\n\r\n",
                "POST /comment HTTP/1.1\r\nHost: example.com\r\nContent-Type: application/x-www-form-urlencoded\r\n\r\ncomment=test"
            ]
            
            print("   - Testing XSS injection point analysis...")
            injection_points = await xss_agent._analyze_injection_points(mock_raw_requests, [])
            
            print(f"   - Found {len(injection_points)} XSS injection points")
            
            if len(injection_points) > 0:
                # Test payload generation
                print("   - Testing XSS payload generation...")
                payloads = await xss_agent._generate_xss_payloads(injection_points[0])
                print(f"   - Generated {len(payloads)} XSS payloads")
                
                return len(payloads) > 0
            
            return len(injection_points) > 0
            
        except Exception as e:
            print(f"   - XSS robustness test failed: {e}")
            return False
    
    async def test_lead_agent(self):
        """Test lead agent orchestration"""
        try:
            lead_agent = LeadAgent()
            
            # Test agent initialization
            print("   - Testing lead agent initialization...")
            
            # Test with mock preprocessor data
            mock_preprocessor_data = {
                "metadata": {"target_url": "https://example.com"},
                "network_logs": [],
                "raw_requests": [],
                "recon": []
            }
            
            print("   - Testing vulnerability analysis coordination...")
            
            # This would normally coordinate other agents
            # For testing, just verify the lead agent can process the data
            result = lead_agent._analyze_preprocessor_data(mock_preprocessor_data)
            
            print("   - Lead agent analysis completed")
            return True
            
        except Exception as e:
            print(f"   - Lead agent test failed: {e}")
            return False
    
    async def test_end_to_end(self):
        """Test end-to-end resilience with a simple target"""
        try:
            print("   - Testing end-to-end resilience...")
            
            # Test with a simple, reliable target
            test_url = "https://httpbin.org/html"
            
            # Test browser agent with fallback
            browser_agent = BrowserAgent(test_url)
            
            print("   - Testing browser agent (will likely use fallback)...")
            
            # This should gracefully fall back to HTTP-based reconnaissance
            result = await asyncio.wait_for(browser_agent.execute(), timeout=90)
            
            if not result:
                print("   - No result from browser agent")
                return False
            
            # Check if we got some data (even from fallback)
            agent_type = result.get("metadata", {}).get("agent_type", "unknown")
            network_count = len(result.get("network_logs", []))
            
            print(f"   - Agent type used: {agent_type}")
            print(f"   - Network logs captured: {network_count}")
            
            # Success if we got any data, regardless of agent type
            return network_count > 0
            
        except Exception as e:
            print(f"   - End-to-end test failed: {e}")
            return False


async def main():
    """Main test function"""
    test_suite = VAPTRobustnessTest()
    success = await test_suite.run_all_tests()
    
    # Save test results
    results_file = Path("test_results.json")
    with open(results_file, 'w') as f:
        json.dump({
            "timestamp": datetime.now().isoformat(),
            "results": test_suite.test_results,
            "summary": {
                "passed": sum(1 for r in test_suite.test_results.values() if r),
                "failed": sum(1 for r in test_suite.test_results.values() if not r),
                "total": len(test_suite.test_results)
            }
        }, f, indent=2)
    
    print(f"\n📄 Test results saved to: {results_file}")
    
    if success:
        print("\n🎯 Recommendations:")
        print("1. ✅ VAPT tool is robust and ready for production use")
        print("2. ✅ Fallback mechanisms are working correctly")
        print("3. ✅ Error handling is comprehensive")
        print("4. ✅ All agents can handle various failure scenarios")
    else:
        print("\n🔧 Recommendations:")
        print("1. ⚠️  Review failed tests and address issues")
        print("2. ⚠️  Test with different network conditions")
        print("3. ⚠️  Verify MCP Playwright installation")
        print("4. ⚠️  Check configuration settings")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
