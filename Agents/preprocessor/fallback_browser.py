"""
Fallback Browser Agent
Provides basic web reconnaissance when <PERSON><PERSON>wright is unavailable
"""

import asyncio
import aiohttp
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import logging

from utils.logging_utils import setup_component_logging
from utils.error_handler import PreprocessorError


class FallbackBrowserAgent:
    """
    Fallback browser agent using aiohttp for basic reconnaissance
    Used when <PERSON><PERSON> <PERSON>wright is unavailable or failing
    """
    
    def __init__(self, target_url: str):
        self.target_url = target_url
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.logger = setup_component_logging(f"fallback_browser_{self.session_id}")
        self.session = None
        self.visited_urls = set()
        self.discovered_forms = []
        self.discovered_links = []
        
        # Initialize report structure
        self.report_data = self._initialize_report_structure()
        
        self.logger.info(f"Fallback Browser Agent initialized for {target_url}")
    
    def _initialize_report_structure(self) -> Dict[str, Any]:
        """Initialize the report data structure"""
        return {
            "metadata": {
                "target_url": self.target_url,
                "start_time": datetime.now().isoformat(),
                "end_time": None,
                "duration": None,
                "session_id": self.session_id,
                "agent_type": "fallback_browser",
                "capabilities": ["basic_http", "html_parsing", "form_discovery", "link_crawling"]
            },
            "recon": [],
            "network_logs": [],
            "console_logs": [
                {
                    "level": "info",
                    "message": "Using fallback browser agent - MCP Playwright unavailable",
                    "timestamp": datetime.now().isoformat(),
                    "source": "fallback_agent"
                }
            ],
            "raw_requests": [],
            "crawl_data": {
                "sitemap": {},
                "visited_urls": [],
                "orphan_pages": [],
                "discovered_forms": [],
                "discovered_links": []
            },
            "component_interactions": [],
            "session_states": [],
            "navigation_paths": []
        }
    
    async def execute(self) -> Dict[str, Any]:
        """Execute fallback reconnaissance"""
        self.logger.info(f"Starting fallback reconnaissance for {self.target_url}")
        start_time = datetime.now()
        
        try:
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                }
            ) as session:
                self.session = session
                
                # Execute reconnaissance tasks
                await self._execute_fallback_tasks()
                
                # Generate raw requests from discovered data
                self._generate_fallback_raw_requests()
                
                # Finalize report
                end_time = datetime.now()
                self.report_data["metadata"]["end_time"] = end_time.isoformat()
                self.report_data["metadata"]["duration"] = str(end_time - start_time)
                
                # Update crawl data
                self.report_data["crawl_data"]["visited_urls"] = list(self.visited_urls)
                self.report_data["crawl_data"]["discovered_forms"] = self.discovered_forms
                self.report_data["crawl_data"]["discovered_links"] = self.discovered_links
                
                # Save report
                report_path = await self._save_report()
                self.logger.info(f"Fallback report saved to: {report_path}")
                
                return self.report_data
                
        except Exception as e:
            self.logger.error(f"Fallback reconnaissance failed: {e}")
            raise PreprocessorError(f"Fallback browser agent failed: {e}", "fallback_browser")
    
    async def _execute_fallback_tasks(self):
        """Execute basic reconnaissance tasks"""
        tasks = [
            ("Basic Page Analysis", self._analyze_main_page),
            ("Form Discovery", self._discover_forms),
            ("Link Discovery", self._discover_links),
            ("Basic Crawling", self._basic_crawl)
        ]
        
        for task_name, task_func in tasks:
            try:
                self.logger.info(f"Executing fallback task: {task_name}")
                result = await task_func()
                
                self.report_data["recon"].append({
                    "task": task_name,
                    "timestamp": datetime.now().isoformat(),
                    "response": result,
                    "status": "completed",
                    "agent_type": "fallback"
                })
                
            except Exception as e:
                self.logger.error(f"Fallback task {task_name} failed: {e}")
                self.report_data["recon"].append({
                    "task": task_name,
                    "timestamp": datetime.now().isoformat(),
                    "error": str(e),
                    "status": "failed",
                    "agent_type": "fallback"
                })
    
    async def _analyze_main_page(self) -> str:
        """Analyze the main page"""
        try:
            async with self.session.get(self.target_url) as response:
                if response.status == 200:
                    content = await response.text()
                    soup = BeautifulSoup(content, 'html.parser')
                    
                    # Extract basic information
                    title = soup.find('title')
                    title_text = title.get_text().strip() if title else "No title"
                    
                    # Count elements
                    forms = soup.find_all('form')
                    links = soup.find_all('a', href=True)
                    inputs = soup.find_all('input')
                    
                    # Look for frameworks/technologies
                    scripts = soup.find_all('script', src=True)
                    framework_indicators = []
                    for script in scripts:
                        src = script.get('src', '').lower()
                        if 'angular' in src:
                            framework_indicators.append('Angular')
                        elif 'react' in src:
                            framework_indicators.append('React')
                        elif 'vue' in src:
                            framework_indicators.append('Vue.js')
                        elif 'jquery' in src:
                            framework_indicators.append('jQuery')
                    
                    self.visited_urls.add(self.target_url)
                    
                    # Create network log entry
                    self.report_data["network_logs"].append({
                        "url": self.target_url,
                        "method": "GET",
                        "status": response.status,
                        "statusText": response.reason,
                        "headers": dict(response.headers),
                        "timestamp": datetime.now().isoformat(),
                        "responseSize": len(content)
                    })
                    
                    return f"Successfully analyzed main page: '{title_text}'. Found {len(forms)} forms, {len(links)} links, {len(inputs)} input fields. Detected frameworks: {', '.join(framework_indicators) if framework_indicators else 'None detected'}."
                
                else:
                    return f"Failed to load main page: HTTP {response.status} {response.reason}"
                    
        except Exception as e:
            return f"Error analyzing main page: {e}"
    
    async def _discover_forms(self) -> str:
        """Discover forms on the main page"""
        try:
            async with self.session.get(self.target_url) as response:
                if response.status == 200:
                    content = await response.text()
                    soup = BeautifulSoup(content, 'html.parser')
                    
                    forms = soup.find_all('form')
                    form_count = 0
                    
                    for form in forms:
                        form_data = {
                            "action": form.get('action', ''),
                            "method": form.get('method', 'GET').upper(),
                            "inputs": []
                        }
                        
                        inputs = form.find_all(['input', 'textarea', 'select'])
                        for inp in inputs:
                            input_data = {
                                "type": inp.get('type', 'text'),
                                "name": inp.get('name', ''),
                                "id": inp.get('id', ''),
                                "placeholder": inp.get('placeholder', ''),
                                "required": inp.has_attr('required')
                            }
                            form_data["inputs"].append(input_data)
                        
                        self.discovered_forms.append(form_data)
                        form_count += 1
                    
                    return f"Discovered {form_count} forms with various input fields for potential testing."
                
                else:
                    return f"Failed to discover forms: HTTP {response.status}"
                    
        except Exception as e:
            return f"Error discovering forms: {e}"
    
    async def _discover_links(self) -> str:
        """Discover links on the main page"""
        try:
            async with self.session.get(self.target_url) as response:
                if response.status == 200:
                    content = await response.text()
                    soup = BeautifulSoup(content, 'html.parser')
                    
                    links = soup.find_all('a', href=True)
                    internal_links = []
                    external_links = []
                    
                    base_domain = urlparse(self.target_url).netloc
                    
                    for link in links:
                        href = link.get('href')
                        full_url = urljoin(self.target_url, href)
                        link_domain = urlparse(full_url).netloc
                        
                        link_data = {
                            "href": href,
                            "full_url": full_url,
                            "text": link.get_text().strip()[:100],  # Limit text length
                            "title": link.get('title', '')
                        }
                        
                        if link_domain == base_domain or not link_domain:
                            internal_links.append(link_data)
                        else:
                            external_links.append(link_data)
                    
                    self.discovered_links = internal_links + external_links
                    
                    return f"Discovered {len(internal_links)} internal links and {len(external_links)} external links."
                
                else:
                    return f"Failed to discover links: HTTP {response.status}"
                    
        except Exception as e:
            return f"Error discovering links: {e}"
    
    async def _basic_crawl(self) -> str:
        """Perform basic crawling of discovered internal links"""
        try:
            internal_links = [link for link in self.discovered_links 
                            if urlparse(link['full_url']).netloc == urlparse(self.target_url).netloc]
            
            crawled_count = 0
            max_crawl = 5  # Limit crawling to avoid timeouts
            
            for link in internal_links[:max_crawl]:
                try:
                    url = link['full_url']
                    if url in self.visited_urls:
                        continue
                    
                    async with self.session.get(url) as response:
                        if response.status == 200:
                            self.visited_urls.add(url)
                            content = await response.text()
                            
                            # Create network log entry
                            self.report_data["network_logs"].append({
                                "url": url,
                                "method": "GET", 
                                "status": response.status,
                                "statusText": response.reason,
                                "headers": dict(response.headers),
                                "timestamp": datetime.now().isoformat(),
                                "responseSize": len(content)
                            })
                            
                            crawled_count += 1
                            
                except Exception as e:
                    self.logger.warning(f"Failed to crawl {link['full_url']}: {e}")
            
            return f"Successfully crawled {crawled_count} additional pages out of {len(internal_links)} discovered internal links."
            
        except Exception as e:
            return f"Error during basic crawling: {e}"
    
    def _generate_fallback_raw_requests(self):
        """Generate raw HTTP requests from network logs"""
        self.logger.info("Generating raw HTTP requests from fallback data...")
        
        for network_log in self.report_data["network_logs"]:
            try:
                method = network_log.get('method', 'GET')
                url = network_log.get('url', '')
                headers = network_log.get('headers', {})
                
                if not url:
                    continue
                
                parsed_url = urlparse(url)
                path = parsed_url.path or '/'
                if parsed_url.query:
                    path += f"?{parsed_url.query}"
                
                # Build raw request
                raw_lines = [f"{method} {path} HTTP/1.1"]
                raw_lines.append(f"Host: {parsed_url.netloc}")
                
                # Add common headers
                raw_lines.append("User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                raw_lines.append("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
                raw_lines.append("Accept-Language: en-US,en;q=0.5")
                raw_lines.append("Accept-Encoding: gzip, deflate")
                raw_lines.append("Connection: keep-alive")
                raw_lines.append("")  # Empty line to end headers
                
                raw_request = "\r\n".join(raw_lines)
                self.report_data["raw_requests"].append(raw_request)
                
            except Exception as e:
                self.logger.error(f"Failed to generate raw request: {e}")
        
        self.logger.info(f"Generated {len(self.report_data['raw_requests'])} raw requests")
    
    async def _save_report(self) -> str:
        """Save the fallback reconnaissance report"""
        reports_dir = Path("reports")
        reports_dir.mkdir(exist_ok=True)
        
        # Generate filename
        parsed_url = urlparse(self.target_url)
        domain = parsed_url.netloc.replace(':', '_')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"fallback_report_{domain}_{timestamp}.json"
        
        report_path = reports_dir / filename
        
        # Save report
        with open(report_path, 'w') as f:
            json.dump(self.report_data, f, indent=2, default=str)
        
        return str(report_path)
