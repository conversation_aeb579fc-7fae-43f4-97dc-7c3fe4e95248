"""
Browser Agent - Preprocessor Component
Performs comprehensive browser-based reconnaissance using Playwright MCP
"""

import asyncio
import json
import logging
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from contextlib import asynccontextmanager
from urllib.parse import urljoin, urlparse

from agno.agent import Agent
from agno.tools.mcp import MCPTools

from config.settings import config
from config.llm_providers import AgnoModelFactory
from utils.logging_utils import setup_component_logging, audit_logger, performance_monitor
from utils.error_handler import async_error_handler, PreprocessorError, mcp_error_handler

class BrowserAgent:
    """
    Browser Agent for comprehensive web reconnaissance
    
    Capabilities:
    - Site mapping and crawling
    - UI component interaction (clicks, scrolls, keyboard input)
    - Network traffic capture
    - Console log monitoring
    - Raw HTTP request generation
    - Session state tracking
    """
    
    def __init__(self, target_url: str):
        self.target_url = target_url
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.logger = setup_component_logging(f"browser_agent_{self.session_id}")
        
        # Initialize report structure
        self.report_data = self._initialize_report_structure()
        
        self.logger.info(f"Browser Agent initialized for {target_url}")
    
    def _initialize_report_structure(self) -> Dict[str, Any]:
        """Initialize the report data structure"""
        return {
            "metadata": {
                "target_url": self.target_url,
                "start_time": datetime.now().isoformat(),
                "end_time": None,
                "duration": None,
                "session_id": self.session_id
            },
            "recon": [],
            "network_logs": [],
            "console_logs": [],
            "raw_requests": [],
            "crawl_data": {
                "sitemap": {},
                "visited_urls": [],
                "orphan_pages": []
            },
            "component_interactions": [],
            "session_states": [],
            "navigation_paths": []
        }
    
    @async_error_handler("browser_agent")
    @performance_monitor
    async def execute(self) -> Dict[str, Any]:
        """Execute comprehensive browser reconnaissance with fallback support"""
        self.logger.info(f"Starting browser reconnaissance for {self.target_url}")
        audit_logger.log_scan_start(self.target_url, "browser_recon", self.session_id)

        start_time = datetime.now()

        # Try MCP Playwright first
        try:
            return await self._execute_with_mcp()

        except Exception as mcp_error:
            self.logger.warning(f"MCP Playwright failed: {mcp_error}")

            # Check if we should try fallback
            if self._should_use_fallback(mcp_error):
                self.logger.info("Attempting fallback browser reconnaissance...")
                try:
                    return await self._execute_with_fallback()
                except Exception as fallback_error:
                    self.logger.error(f"Fallback also failed: {fallback_error}")
                    # Return partial results if available
                    return await self._create_minimal_report(str(mcp_error), str(fallback_error))
            else:
                # MCP error is not recoverable, fail fast
                raise PreprocessorError(f"Browser reconnaissance failed: {mcp_error}", "browser_agent")

    async def _execute_with_mcp(self) -> Dict[str, Any]:
        """Execute reconnaissance using MCP Playwright"""
        start_time = datetime.now()

        async with self._get_mcp_tools() as mcp_tools:
            # Create AI agent for intelligent reconnaissance
            agent = Agent(
                name="Browser Reconnaissance Agent",
                tools=[mcp_tools],
                model=AgnoModelFactory.create_agno_model(),
                debug_mode=config.general.debug_mode if config else False,
                instructions=self._get_agent_instructions()
            )

            # Execute reconnaissance tasks
            await self._execute_reconnaissance_tasks(agent)

            # Extract browser data (network logs, console logs)
            await self._extract_browser_data(agent)

            # Generate raw requests from captured data
            self._generate_raw_requests()

            # Finalize report
            end_time = datetime.now()
            self.report_data["metadata"]["end_time"] = end_time.isoformat()
            self.report_data["metadata"]["duration"] = str(end_time - start_time)
            self.report_data["metadata"]["agent_type"] = "mcp_playwright"

            # Save report
            report_path = await self._save_report()
            self.logger.info(f"MCP report saved to: {report_path}")

            audit_logger.log_scan_complete(self.target_url, "browser_recon", self.session_id, str(end_time - start_time))

            return self.report_data

    async def _execute_with_fallback(self) -> Dict[str, Any]:
        """Execute reconnaissance using fallback browser agent"""
        from .fallback_browser import FallbackBrowserAgent

        self.logger.info("Using fallback browser agent")
        fallback_agent = FallbackBrowserAgent(self.target_url)

        # Execute fallback reconnaissance
        fallback_result = await fallback_agent.execute()

        # Merge fallback results with our report structure
        self.report_data.update(fallback_result)
        self.report_data["metadata"]["agent_type"] = "fallback_http"
        self.report_data["metadata"]["fallback_reason"] = "MCP Playwright unavailable"

        # Save report
        report_path = await self._save_report()
        self.logger.info(f"Fallback report saved to: {report_path}")

        audit_logger.log_scan_complete(self.target_url, "browser_recon", self.session_id,
                                     self.report_data["metadata"]["duration"])

        return self.report_data

    def _should_use_fallback(self, error: Exception) -> bool:
        """Determine if we should use fallback based on the error"""
        error_str = str(error).lower()

        # Use fallback for these types of errors
        fallback_triggers = [
            "playwright-mcp not found",
            "failed to start mcp server",
            "mcp connection failed",
            "connection refused",
            "timeout",
            "eaddrinuse",
            "no such file or directory"
        ]

        return any(trigger in error_str for trigger in fallback_triggers)

    async def _create_minimal_report(self, mcp_error: str, fallback_error: str) -> Dict[str, Any]:
        """Create a minimal report when both MCP and fallback fail"""
        end_time = datetime.now()

        minimal_report = {
            "metadata": {
                "target_url": self.target_url,
                "start_time": self.report_data["metadata"]["start_time"],
                "end_time": end_time.isoformat(),
                "duration": str(end_time - datetime.fromisoformat(self.report_data["metadata"]["start_time"])),
                "session_id": self.session_id,
                "agent_type": "minimal_fallback",
                "status": "failed"
            },
            "recon": [
                {
                    "task": "Browser Reconnaissance",
                    "timestamp": end_time.isoformat(),
                    "error": f"Both MCP and fallback failed. MCP: {mcp_error}. Fallback: {fallback_error}",
                    "status": "failed"
                }
            ],
            "network_logs": [],
            "console_logs": [
                {
                    "level": "error",
                    "message": f"Browser reconnaissance failed: MCP error: {mcp_error}",
                    "timestamp": end_time.isoformat(),
                    "source": "browser_agent"
                },
                {
                    "level": "error",
                    "message": f"Fallback also failed: {fallback_error}",
                    "timestamp": end_time.isoformat(),
                    "source": "fallback_agent"
                }
            ],
            "raw_requests": [],
            "crawl_data": {"sitemap": {}, "visited_urls": [], "orphan_pages": []},
            "component_interactions": [],
            "session_states": [],
            "navigation_paths": []
        }

        # Save minimal report
        report_path = await self._save_minimal_report(minimal_report)
        self.logger.info(f"Minimal report saved to: {report_path}")

        return minimal_report PreprocessorError(f"Browser reconnaissance failed: {e}", "browser_agent")
    
    @asynccontextmanager
    async def _get_mcp_tools(self):
        """Get MCP tools context manager with robust error handling"""
        mcp_tools = None
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                # Find playwright-mcp directory
                current_dir = Path(__file__).parent
                playwright_mcp_path = None

                # Search for playwright-mcp in common locations
                search_paths = [
                    current_dir.parent / "playwright-mcp",
                    current_dir.parent.parent / "playwright-mcp",
                    current_dir.parent.parent.parent / "playwright-mcp",
                    Path.cwd() / "playwright-mcp"
                ]

                for path in search_paths:
                    if (path / "cli.js").exists():
                        playwright_mcp_path = path / "cli.js"
                        break

                if not playwright_mcp_path:
                    raise PreprocessorError("playwright-mcp not found. Please clone and build it first.", "browser_agent")

                self.logger.info(f"Starting MCP server instance (attempt {retry_count + 1}/{max_retries})")

                # Enhanced MCP configuration
                mcp_tools = MCPTools(
                    command=f"node {playwright_mcp_path} --browser {config.playwright.browser if config else 'chrome'} --headless",
                    env={
                        "NODE_ENV": config.playwright.node_env if config else "production",
                        "PLAYWRIGHT_BROWSERS_PATH": "0",
                        "DEBUG": "pw:mcp:*" if config and config.general.debug_mode else ""
                    },
                    timeout_seconds=config.playwright.timeout_seconds if config else 30,
                    transport="stdio"
                )

                await mcp_tools.__aenter__()

                # Test MCP connection with a simple tool call
                await self._test_mcp_connection(mcp_tools)

                self.logger.info("MCP server started successfully")
                yield mcp_tools
                break

            except Exception as e:
                retry_count += 1
                mcp_error_handler.increment_retry()

                if mcp_tools:
                    try:
                        await mcp_tools.__aexit__(None, None, None)
                    except:
                        pass
                    mcp_tools = None

                if mcp_error_handler.should_retry(e) and retry_count < max_retries:
                    self.logger.warning(f"MCP connection failed (attempt {retry_count}/{max_retries}): {e}")
                    await asyncio.sleep(2 ** retry_count)  # Exponential backoff
                    continue
                else:
                    self.logger.error(f"MCP connection failed after {retry_count} attempts: {e}")
                    raise PreprocessorError(f"Failed to start MCP server after {retry_count} attempts: {e}", "browser_agent")

        # Cleanup
        if mcp_tools:
            try:
                await mcp_tools.__aexit__(None, None, None)
            except Exception as e:
                self.logger.error(f"MCP tools cleanup error: {e}")

    async def _test_mcp_connection(self, mcp_tools):
        """Test MCP connection with a simple tool call"""
        try:
            # Try to list available tools
            tools = await mcp_tools.list_tools()
            if not tools:
                raise Exception("No tools available from MCP server")
            self.logger.info(f"MCP server has {len(tools)} tools available")
        except Exception as e:
            raise Exception(f"MCP connection test failed: {e}")
    
    def _get_agent_instructions(self) -> str:
        """Get instructions for the AI agent"""
        return f"""
You are a professional web security reconnaissance agent. Your task is to thoroughly analyze the website {self.target_url}.

OBJECTIVES:
1. Navigate and explore the website comprehensively
2. Interact with all UI components (forms, buttons, links, etc.)
3. Discover all pages and endpoints
4. Test different user interactions (clicks, scrolls, keyboard input)
5. Capture all network traffic and console logs

METHODOLOGY:
1. Start by navigating to the main page
2. Systematically explore all links and navigation elements
3. Fill out forms with test data and submit them
4. Click on all interactive elements
5. Scroll through pages to trigger dynamic content loading
6. Test keyboard interactions where applicable
7. Navigate through different user flows

IMPORTANT:
- Be thorough but respectful of the target
- Don't perform any destructive actions
- Focus on discovery and mapping
- Capture all network requests and responses
- Log any errors or interesting findings

Use the available Playwright MCP tools to interact with the browser and capture data.
"""
    
    async def _execute_reconnaissance_tasks(self, agent: Agent):
        """Execute reconnaissance tasks using the AI agent with robust error handling"""
        tasks = [
            "Initial Reconnaissance",
            "Comprehensive Site Crawl",
            "Component Interaction Testing",
            "Authentication Flow Analysis"
        ]

        for task in tasks:
            max_retries = 2
            retry_count = 0
            task_completed = False

            while retry_count <= max_retries and not task_completed:
                try:
                    self.logger.info(f"Executing task: {task} (attempt {retry_count + 1}/{max_retries + 1})")

                    task_prompt = self._get_task_prompt(task)

                    # Add error recovery instructions for retries
                    if retry_count > 0:
                        task_prompt += f"""

                        IMPORTANT: This is retry attempt {retry_count + 1}. If you encounter errors:
                        1. Use browser_snapshot first to see current page state
                        2. Navigate to the target URL if needed
                        3. Be more cautious with element interactions
                        4. Skip problematic elements and continue with others
                        5. Focus on gathering available information rather than perfect execution
                        """

                    response = await asyncio.wait_for(
                        agent.arun(task_prompt),
                        timeout=120  # 2 minute timeout per task
                    )

                    # Store successful task result
                    self.report_data["recon"].append({
                        "task": task,
                        "timestamp": datetime.now().isoformat(),
                        "response": response.content if hasattr(response, 'content') else str(response),
                        "status": "completed",
                        "attempts": retry_count + 1
                    })

                    task_completed = True
                    self.logger.info(f"Task {task} completed successfully")

                except asyncio.TimeoutError:
                    retry_count += 1
                    self.logger.warning(f"Task {task} timed out (attempt {retry_count}/{max_retries + 1})")
                    if retry_count > max_retries:
                        self.report_data["recon"].append({
                            "task": task,
                            "timestamp": datetime.now().isoformat(),
                            "error": "Task timed out after multiple attempts",
                            "status": "failed",
                            "attempts": retry_count
                        })

                except Exception as e:
                    retry_count += 1
                    error_str = str(e).lower()

                    # Check if this is a recoverable MCP/Playwright error
                    if any(err in error_str for err in ["net::err_aborted", "execution context", "ref not found", "no current snapshot"]):
                        self.logger.warning(f"Task {task} encountered recoverable error (attempt {retry_count}/{max_retries + 1}): {e}")
                        if retry_count <= max_retries:
                            await asyncio.sleep(2)  # Brief pause before retry
                            continue

                    # Non-recoverable error or max retries reached
                    self.logger.error(f"Task {task} failed after {retry_count} attempts: {e}")
                    self.report_data["recon"].append({
                        "task": task,
                        "timestamp": datetime.now().isoformat(),
                        "error": str(e),
                        "status": "failed",
                        "attempts": retry_count,
                        "error_type": "non_recoverable" if retry_count == 1 else "max_retries_exceeded"
                    })
                    break
    
    def _get_task_prompt(self, task: str) -> str:
        """Get specific prompt for each task with error resilience"""
        base_instructions = f"""
IMPORTANT ERROR HANDLING GUIDELINES:
- If you encounter "net::ERR_ABORTED" errors, the target may be blocking automated requests - continue with available data
- If you get "Execution context destroyed" errors, take a new snapshot and navigate again
- If elements are "not found", take a snapshot first to see current page state
- If navigation fails, try alternative approaches or skip problematic pages
- Always prioritize gathering available information over perfect execution
- Use browser_snapshot frequently to understand current page state
- Be patient with slow-loading pages and dynamic content

Target URL: {self.target_url}
"""

        prompts = {
            "Initial Reconnaissance": base_instructions + f"""
TASK: Navigate to {self.target_url} and perform initial reconnaissance

STEPS:
1. Use browser_navigate to load the main page
2. Take a browser_snapshot to capture initial state
3. Identify the site structure and navigation elements
4. Look for login forms, search boxes, and interactive elements
5. Note any technologies, frameworks, or interesting features
6. If navigation fails, document the error and any partial information gathered

RESILIENCE: If the main page fails to load, try:
- Waiting a few seconds and retrying
- Checking if there's a redirect or different URL structure
- Documenting any error messages or network issues
""",
            "Comprehensive Site Crawl": base_instructions + f"""
TASK: Perform comprehensive crawling of {self.target_url}

STEPS:
1. Start from the main page (navigate if needed)
2. Take snapshots before and after navigation
3. Follow internal links systematically (limit to 10-15 pages to avoid timeouts)
4. Map the site structure and identify page types
5. Note any access restrictions or authentication requirements
6. Document discovered endpoints and URL patterns

RESILIENCE: If crawling encounters errors:
- Skip problematic links and continue with others
- Focus on main navigation and important pages
- Document partial results rather than failing completely
- Use browser_snapshot to verify page state before interactions
""",
            "Component Interaction Testing": base_instructions + f"""
TASK: Test interactive components on {self.target_url}

STEPS:
1. Ensure you're on the target page (navigate if needed)
2. Take a snapshot to see available elements
3. Identify and interact with forms, buttons, and links
4. Fill forms with safe test data (<EMAIL>, password123, etc.)
5. Test dropdown menus and navigation elements
6. Scroll to trigger dynamic content loading
7. Document all interactions and responses

RESILIENCE: If interactions fail:
- Take snapshots before each major interaction
- Skip elements that cause errors and continue with others
- Use simple, safe test data for forms
- Don't retry failed interactions more than once
- Focus on gathering information about available components
""",
            "Authentication Flow Analysis": base_instructions + f"""
TASK: Analyze authentication and user flows on {self.target_url}

STEPS:
1. Navigate to the target and take a snapshot
2. Look for login/registration forms and links
3. Test authentication flows with dummy credentials (<EMAIL> / password123)
4. Identify session management mechanisms
5. Check for password reset and account recovery flows
6. Document security features and user flow patterns

RESILIENCE: If authentication testing fails:
- Don't use real credentials under any circumstances
- Skip failed login attempts and focus on form analysis
- Document form fields and validation patterns
- Look for alternative authentication methods (social login, etc.)
- Gather information about security features without breaking them

SAFETY: Never attempt to:
- Use real credentials or personal information
- Bypass security measures
- Perform actions that could affect real user accounts
- Submit forms with malicious or harmful data
"""
        }

        return prompts.get(task, base_instructions + f"Analyze {self.target_url} for the task: {task}")
    
    async def _extract_browser_data(self, agent: Agent):
        """Extract network logs, console logs, and generate raw requests with robust error handling"""
        self.logger.info("Extracting browser data...")

        # Extract network logs with retry logic
        network_success = False
        for attempt in range(3):
            try:
                self.logger.info(f"Extracting network logs (attempt {attempt + 1}/3)...")

                network_prompt = """
TASK: Extract all network requests captured during the browser session.

STEPS:
1. Use browser_network_requests tool to get all captured network requests
2. If that fails, try alternative methods to access network data
3. Return the data in a structured format

IMPORTANT: If the tool fails or returns no data:
- Document that network capture may not be available
- Return whatever partial data is accessible
- Don't fail the entire extraction process

Please extract network request data and format it as JSON.
"""

                network_response = await asyncio.wait_for(
                    agent.arun(network_prompt),
                    timeout=30
                )

                if network_response and hasattr(network_response, 'content'):
                    self._process_network_logs(network_response.content)
                    network_success = True
                    break

            except asyncio.TimeoutError:
                self.logger.warning(f"Network log extraction timed out (attempt {attempt + 1}/3)")
            except Exception as e:
                self.logger.warning(f"Network log extraction failed (attempt {attempt + 1}/3): {e}")
                if attempt == 2:  # Last attempt
                    self.logger.error(f"Failed to extract network logs after 3 attempts: {e}")

        if not network_success:
            self.logger.warning("Network log extraction failed - continuing without network data")

        # Extract console logs with retry logic
        console_success = False
        for attempt in range(3):
            try:
                self.logger.info(f"Extracting console logs (attempt {attempt + 1}/3)...")

                console_prompt = """
TASK: Extract all console messages captured during the browser session.

STEPS:
1. Use browser_console_messages tool to get all console logs
2. If that fails, try alternative methods to access console data
3. Return the data in a structured format

IMPORTANT: If the tool fails or returns no data:
- Document that console capture may not be available
- Return whatever partial data is accessible
- Don't fail the entire extraction process

Please extract console message data and format it as JSON.
"""

                console_response = await asyncio.wait_for(
                    agent.arun(console_prompt),
                    timeout=30
                )

                if console_response and hasattr(console_response, 'content'):
                    self._process_console_logs(console_response.content)
                    console_success = True
                    break

            except asyncio.TimeoutError:
                self.logger.warning(f"Console log extraction timed out (attempt {attempt + 1}/3)")
            except Exception as e:
                self.logger.warning(f"Console log extraction failed (attempt {attempt + 1}/3): {e}")
                if attempt == 2:  # Last attempt
                    self.logger.error(f"Failed to extract console logs after 3 attempts: {e}")

        if not console_success:
            self.logger.warning("Console log extraction failed - continuing without console data")

        # Log extraction summary
        network_count = len(self.report_data.get("network_logs", []))
        console_count = len(self.report_data.get("console_logs", []))
        self.logger.info(f"Data extraction completed: {network_count} network requests, {console_count} console messages")
    
    def _process_network_logs(self, agent_response_content: str):
        """Process and store network logs"""
        self.logger.info("Processing network logs...")
        try:
            # Try to extract JSON from the response
            import re
            json_match = re.search(r'\{.*\}', agent_response_content, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group(0))
                requests = data.get("requests", data.get("network_requests", []))
                if isinstance(requests, list):
                    self.report_data["network_logs"].extend(requests)
                    self.logger.info(f"Processed {len(requests)} network requests")
        except Exception as e:
            self.logger.error(f"Failed to process network logs: {e}")
    
    def _process_console_logs(self, agent_response_content: str):
        """Process and store console logs"""
        self.logger.info("Processing console logs...")
        try:
            # Try to extract JSON from the response
            import re
            json_match = re.search(r'\{.*\}', agent_response_content, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group(0))
                logs = data.get("logs", data.get("console_logs", []))
                if isinstance(logs, list):
                    self.report_data["console_logs"].extend(logs)
                    self.logger.info(f"Processed {len(logs)} console messages")
        except Exception as e:
            self.logger.error(f"Failed to process console logs: {e}")

    def _generate_raw_requests(self):
        """Generate raw HTTP requests from network logs"""
        self.logger.info("Generating raw HTTP requests...")

        for network_log in self.report_data["network_logs"]:
            try:
                raw_request = self._convert_to_raw_request(network_log)
                if raw_request:
                    self.report_data["raw_requests"].append(raw_request)
            except Exception as e:
                self.logger.error(f"Failed to convert network log to raw request: {e}")

        self.logger.info(f"Generated {len(self.report_data['raw_requests'])} raw requests")

    def _convert_to_raw_request(self, network_log: Dict[str, Any]) -> Optional[str]:
        """Convert network log entry to complete raw HTTP request"""
        try:
            method = network_log.get('method', 'GET')
            url = network_log.get('url', '')
            headers = network_log.get('headers', {})
            request_headers = network_log.get('requestHeaders', {})
            body = network_log.get('postData', '')

            if not url:
                return None

            # Parse URL
            parsed_url = urlparse(url)
            path = parsed_url.path or '/'
            if parsed_url.query:
                path += f"?{parsed_url.query}"

            # Build raw request lines
            raw_lines = [f"{method} {path} HTTP/1.1"]

            # Add Host header first
            raw_lines.append(f"Host: {parsed_url.netloc}")

            # Merge headers from different sources
            all_headers = {}
            if isinstance(headers, dict):
                all_headers.update(headers)
            if isinstance(request_headers, dict):
                all_headers.update(request_headers)

            # Add standard headers if not present
            if 'user-agent' not in [h.lower() for h in all_headers.keys()]:
                all_headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'

            if 'accept' not in [h.lower() for h in all_headers.keys()]:
                if method.upper() == 'GET':
                    all_headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
                else:
                    all_headers['Accept'] = 'application/json, text/plain, */*'

            if 'accept-language' not in [h.lower() for h in all_headers.keys()]:
                all_headers['Accept-Language'] = 'en-US,en;q=0.9'

            if 'accept-encoding' not in [h.lower() for h in all_headers.keys()]:
                all_headers['Accept-Encoding'] = 'gzip, deflate, br'

            if 'connection' not in [h.lower() for h in all_headers.keys()]:
                all_headers['Connection'] = 'keep-alive'

            # Add Content-Type and Content-Length for POST requests
            if method.upper() in ['POST', 'PUT', 'PATCH'] and body:
                if 'content-type' not in [h.lower() for h in all_headers.keys()]:
                    # Determine content type based on body
                    if body.strip().startswith('{') or body.strip().startswith('['):
                        all_headers['Content-Type'] = 'application/json'
                    elif '=' in body and '&' in body:
                        all_headers['Content-Type'] = 'application/x-www-form-urlencoded'
                    else:
                        all_headers['Content-Type'] = 'text/plain'

                if 'content-length' not in [h.lower() for h in all_headers.keys()]:
                    all_headers['Content-Length'] = str(len(body.encode('utf-8')))

            # Add headers (skip Host as we already added it)
            for name, value in all_headers.items():
                if name.lower() != 'host':
                    raw_lines.append(f"{name}: {value}")

            # Add empty line before body
            raw_lines.append("")

            # Add body if present
            if body:
                raw_lines.append(body)

            return "\r\n".join(raw_lines)

        except Exception as e:
            self.logger.error(f"Error converting network log to raw request: {e}")
            return None

    async def _save_report(self) -> str:
        """Save the reconnaissance report"""
        if not config:
            return ""

        reports_dir = Path(config.general.reports_dir)
        reports_dir.mkdir(exist_ok=True)

        # Clean URL for filename
        clean_url = self.target_url.replace('https://', '').replace('http://', '').replace('/', '_').replace(':', '_')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        report_filename = f"preprocessor_report_{clean_url}_{timestamp}.json"
        report_path = reports_dir / report_filename

        with open(report_path, 'w') as f:
            json.dump(self.report_data, f, indent=2, default=str)

        return str(report_path)
