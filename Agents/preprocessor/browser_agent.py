"""
Browser Agent - Preprocessor Component
Performs comprehensive browser-based reconnaissance using Playwright MCP
"""

import asyncio
import json
import logging
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from contextlib import asynccontextmanager
from urllib.parse import urljoin, urlparse

from agno.agent import Agent
from agno.tools.mcp import MCPTools

from config.settings import config
from config.llm_providers import AgnoModelFactory
from utils.logging_utils import setup_component_logging, audit_logger, performance_monitor
from utils.error_handler import async_error_handler, PreprocessorError, mcp_error_handler

class BrowserAgent:
    """
    Browser Agent for comprehensive web reconnaissance
    
    Capabilities:
    - Site mapping and crawling
    - UI component interaction (clicks, scrolls, keyboard input)
    - Network traffic capture
    - Console log monitoring
    - Raw HTTP request generation
    - Session state tracking
    """
    
    def __init__(self, target_url: str):
        self.target_url = target_url
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.logger = setup_component_logging(f"browser_agent_{self.session_id}")
        
        # Initialize report structure
        self.report_data = self._initialize_report_structure()
        
        self.logger.info(f"Browser Agent initialized for {target_url}")
    
    def _initialize_report_structure(self) -> Dict[str, Any]:
        """Initialize the report data structure"""
        return {
            "metadata": {
                "target_url": self.target_url,
                "start_time": datetime.now().isoformat(),
                "end_time": None,
                "duration": None,
                "session_id": self.session_id
            },
            "recon": [],
            "network_logs": [],
            "console_logs": [],
            "raw_requests": [],
            "crawl_data": {
                "sitemap": {},
                "visited_urls": [],
                "orphan_pages": []
            },
            "component_interactions": [],
            "session_states": [],
            "navigation_paths": []
        }
    
    @async_error_handler("browser_agent")
    @performance_monitor
    async def execute(self) -> Dict[str, Any]:
        """Execute comprehensive browser reconnaissance"""
        self.logger.info(f"Starting browser reconnaissance for {self.target_url}")
        audit_logger.log_scan_start(self.target_url, "browser_recon", self.session_id)
        
        start_time = datetime.now()
        
        try:
            async with self._get_mcp_tools() as mcp_tools:
                # Create AI agent for intelligent reconnaissance
                agent = Agent(
                    name="Browser Reconnaissance Agent",
                    tools=[mcp_tools],
                    model=AgnoModelFactory.create_agno_model(),
                    debug_mode=config.general.debug_mode if config else False,
                    instructions=self._get_agent_instructions()
                )
                
                # Execute reconnaissance tasks
                await self._execute_reconnaissance_tasks(agent)
                
                # Extract browser data (network logs, console logs)
                await self._extract_browser_data(agent)
                
                # Generate raw requests from captured data
                self._generate_raw_requests()
                
                # Finalize report
                end_time = datetime.now()
                self.report_data["metadata"]["end_time"] = end_time.isoformat()
                self.report_data["metadata"]["duration"] = str(end_time - start_time)
                
                # Save report
                report_path = await self._save_report()
                self.logger.info(f"Report saved to: {report_path}")
                
                audit_logger.log_scan_complete(self.target_url, "browser_recon", self.session_id, str(end_time - start_time))
                
                return self.report_data
                
        except Exception as e:
            self.logger.error(f"Browser reconnaissance failed: {e}")
            raise PreprocessorError(f"Browser reconnaissance failed: {e}", "browser_agent")
    
    @asynccontextmanager
    async def _get_mcp_tools(self):
        """Get MCP tools context manager"""
        mcp_tools = None
        try:
            # Start MCP server using stdio transport
            import os
            
            # Find playwright-mcp directory
            current_dir = Path(__file__).parent
            playwright_mcp_path = None
            
            # Search for playwright-mcp in common locations
            search_paths = [
                current_dir.parent.parent / "playwright-mcp",
                current_dir.parent.parent.parent / "playwright-mcp",
                Path.cwd() / "playwright-mcp"
            ]
            
            for path in search_paths:
                if (path / "cli.js").exists():
                    playwright_mcp_path = path / "cli.js"
                    break
            
            if not playwright_mcp_path:
                raise PreprocessorError("playwright-mcp not found. Please clone and build it first.", "browser_agent")
            
            self.logger.info("Starting new MCP server instance")
            
            mcp_tools = MCPTools(
                command=f"node {playwright_mcp_path} --browser {config.playwright.browser if config else 'chrome'}",
                env={
                    "NODE_ENV": config.playwright.node_env if config else "production",
                    "PLAYWRIGHT_BROWSERS_PATH": "0"
                },
                timeout_seconds=config.playwright.timeout_seconds if config else 30,
                transport="stdio"
            )
            
            await mcp_tools.__aenter__()
            yield mcp_tools
            
        except Exception as e:
            if mcp_error_handler.handle_connection_error(e):
                # Retry logic could go here
                pass
            raise PreprocessorError(f"Failed to start MCP server: {e}", "browser_agent")
        finally:
            if mcp_tools:
                try:
                    await mcp_tools.__aexit__(None, None, None)
                except Exception as e:
                    self.logger.error(f"MCP tools cleanup error: {e}")
    
    def _get_agent_instructions(self) -> str:
        """Get instructions for the AI agent"""
        return f"""
You are a professional web security reconnaissance agent. Your task is to thoroughly analyze the website {self.target_url}.

OBJECTIVES:
1. Navigate and explore the website comprehensively
2. Interact with all UI components (forms, buttons, links, etc.)
3. Discover all pages and endpoints
4. Test different user interactions (clicks, scrolls, keyboard input)
5. Capture all network traffic and console logs

METHODOLOGY:
1. Start by navigating to the main page
2. Systematically explore all links and navigation elements
3. Fill out forms with test data and submit them
4. Click on all interactive elements
5. Scroll through pages to trigger dynamic content loading
6. Test keyboard interactions where applicable
7. Navigate through different user flows

IMPORTANT:
- Be thorough but respectful of the target
- Don't perform any destructive actions
- Focus on discovery and mapping
- Capture all network requests and responses
- Log any errors or interesting findings

Use the available Playwright MCP tools to interact with the browser and capture data.
"""
    
    async def _execute_reconnaissance_tasks(self, agent: Agent):
        """Execute reconnaissance tasks using the AI agent"""
        tasks = [
            "Initial Reconnaissance",
            "Comprehensive Site Crawl", 
            "Component Interaction Testing",
            "Authentication Flow Analysis"
        ]
        
        for task in tasks:
            try:
                self.logger.info(f"Executing task: {task}")
                
                task_prompt = self._get_task_prompt(task)
                response = await agent.arun(task_prompt)
                
                # Store task result
                self.report_data["recon"].append({
                    "task": task,
                    "timestamp": datetime.now().isoformat(),
                    "response": response.content if hasattr(response, 'content') else str(response),
                    "status": "completed"
                })
                
            except Exception as e:
                self.logger.error(f"Task {task} failed: {e}")
                self.report_data["recon"].append({
                    "task": task,
                    "timestamp": datetime.now().isoformat(),
                    "error": str(e),
                    "status": "failed"
                })
    
    def _get_task_prompt(self, task: str) -> str:
        """Get specific prompt for each task"""
        prompts = {
            "Initial Reconnaissance": f"""
Navigate to {self.target_url} and perform initial reconnaissance:
1. Load the main page
2. Identify the site structure and navigation
3. Look for login forms, search boxes, and other interactive elements
4. Take note of any interesting technologies or frameworks used
5. Capture the initial page state
""",
            "Comprehensive Site Crawl": f"""
Perform comprehensive crawling of {self.target_url}:
1. Follow all internal links systematically
2. Discover all pages and endpoints
3. Map the site structure
4. Identify different page types (static, dynamic, API endpoints)
5. Note any access restrictions or authentication requirements
""",
            "Component Interaction Testing": f"""
Test all interactive components on {self.target_url}:
1. Click all buttons and links
2. Fill out and submit all forms with test data
3. Test dropdown menus and navigation elements
4. Scroll through pages to trigger dynamic loading
5. Test keyboard interactions and shortcuts
6. Interact with any JavaScript-heavy components
""",
            "Authentication Flow Analysis": f"""
Analyze authentication and user flows on {self.target_url}:
1. Look for login/registration forms
2. Test the authentication flow (don't use real credentials)
3. Identify session management mechanisms
4. Check for password reset and account recovery flows
5. Test logout functionality if applicable
"""
        }
        
        return prompts.get(task, f"Analyze {self.target_url} for the task: {task}")
    
    async def _extract_browser_data(self, agent: Agent):
        """Extract network logs, console logs, and generate raw requests"""
        self.logger.info("Extracting browser data...")
        
        try:
            # Extract network logs
            network_prompt = """
Use the `mcp_playwright_browser_network_requests` tool to get all network requests.
Return the data as a JSON object with network requests.
"""
            network_response = await agent.arun(network_prompt)
            if network_response and hasattr(network_response, 'content'):
                self._process_network_logs(network_response.content)
            
            # Extract console logs  
            console_prompt = """
Use the `mcp_playwright_browser_console_messages` tool to get all console messages.
Return the data as a JSON object with console logs.
"""
            console_response = await agent.arun(console_prompt)
            if console_response and hasattr(console_response, 'content'):
                self._process_console_logs(console_response.content)
                
        except Exception as e:
            self.logger.error(f"Failed to extract browser data: {e}")
    
    def _process_network_logs(self, agent_response_content: str):
        """Process and store network logs"""
        self.logger.info("Processing network logs...")
        try:
            # Try to extract JSON from the response
            import re
            json_match = re.search(r'\{.*\}', agent_response_content, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group(0))
                requests = data.get("requests", data.get("network_requests", []))
                if isinstance(requests, list):
                    self.report_data["network_logs"].extend(requests)
                    self.logger.info(f"Processed {len(requests)} network requests")
        except Exception as e:
            self.logger.error(f"Failed to process network logs: {e}")
    
    def _process_console_logs(self, agent_response_content: str):
        """Process and store console logs"""
        self.logger.info("Processing console logs...")
        try:
            # Try to extract JSON from the response
            import re
            json_match = re.search(r'\{.*\}', agent_response_content, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group(0))
                logs = data.get("logs", data.get("console_logs", []))
                if isinstance(logs, list):
                    self.report_data["console_logs"].extend(logs)
                    self.logger.info(f"Processed {len(logs)} console messages")
        except Exception as e:
            self.logger.error(f"Failed to process console logs: {e}")

    def _generate_raw_requests(self):
        """Generate raw HTTP requests from network logs"""
        self.logger.info("Generating raw HTTP requests...")

        for network_log in self.report_data["network_logs"]:
            try:
                raw_request = self._convert_to_raw_request(network_log)
                if raw_request:
                    self.report_data["raw_requests"].append(raw_request)
            except Exception as e:
                self.logger.error(f"Failed to convert network log to raw request: {e}")

        self.logger.info(f"Generated {len(self.report_data['raw_requests'])} raw requests")

    def _convert_to_raw_request(self, network_log: Dict[str, Any]) -> Optional[str]:
        """Convert network log entry to complete raw HTTP request"""
        try:
            method = network_log.get('method', 'GET')
            url = network_log.get('url', '')
            headers = network_log.get('headers', {})
            request_headers = network_log.get('requestHeaders', {})
            body = network_log.get('postData', '')

            if not url:
                return None

            # Parse URL
            parsed_url = urlparse(url)
            path = parsed_url.path or '/'
            if parsed_url.query:
                path += f"?{parsed_url.query}"

            # Build raw request lines
            raw_lines = [f"{method} {path} HTTP/1.1"]

            # Add Host header first
            raw_lines.append(f"Host: {parsed_url.netloc}")

            # Merge headers from different sources
            all_headers = {}
            if isinstance(headers, dict):
                all_headers.update(headers)
            if isinstance(request_headers, dict):
                all_headers.update(request_headers)

            # Add standard headers if not present
            if 'user-agent' not in [h.lower() for h in all_headers.keys()]:
                all_headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'

            if 'accept' not in [h.lower() for h in all_headers.keys()]:
                if method.upper() == 'GET':
                    all_headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
                else:
                    all_headers['Accept'] = 'application/json, text/plain, */*'

            if 'accept-language' not in [h.lower() for h in all_headers.keys()]:
                all_headers['Accept-Language'] = 'en-US,en;q=0.9'

            if 'accept-encoding' not in [h.lower() for h in all_headers.keys()]:
                all_headers['Accept-Encoding'] = 'gzip, deflate, br'

            if 'connection' not in [h.lower() for h in all_headers.keys()]:
                all_headers['Connection'] = 'keep-alive'

            # Add Content-Type and Content-Length for POST requests
            if method.upper() in ['POST', 'PUT', 'PATCH'] and body:
                if 'content-type' not in [h.lower() for h in all_headers.keys()]:
                    # Determine content type based on body
                    if body.strip().startswith('{') or body.strip().startswith('['):
                        all_headers['Content-Type'] = 'application/json'
                    elif '=' in body and '&' in body:
                        all_headers['Content-Type'] = 'application/x-www-form-urlencoded'
                    else:
                        all_headers['Content-Type'] = 'text/plain'

                if 'content-length' not in [h.lower() for h in all_headers.keys()]:
                    all_headers['Content-Length'] = str(len(body.encode('utf-8')))

            # Add headers (skip Host as we already added it)
            for name, value in all_headers.items():
                if name.lower() != 'host':
                    raw_lines.append(f"{name}: {value}")

            # Add empty line before body
            raw_lines.append("")

            # Add body if present
            if body:
                raw_lines.append(body)

            return "\r\n".join(raw_lines)

        except Exception as e:
            self.logger.error(f"Error converting network log to raw request: {e}")
            return None

    async def _save_report(self) -> str:
        """Save the reconnaissance report"""
        if not config:
            return ""

        reports_dir = Path(config.general.reports_dir)
        reports_dir.mkdir(exist_ok=True)

        # Clean URL for filename
        clean_url = self.target_url.replace('https://', '').replace('http://', '').replace('/', '_').replace(':', '_')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        report_filename = f"preprocessor_report_{clean_url}_{timestamp}.json"
        report_path = reports_dir / report_filename

        with open(report_path, 'w') as f:
            json.dump(self.report_data, f, indent=2, default=str)

        return str(report_path)
