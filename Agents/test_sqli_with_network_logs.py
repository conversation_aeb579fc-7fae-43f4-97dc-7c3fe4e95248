#!/usr/bin/env python3
"""
Test SQLi Agent with Network Logs
Demonstrates how network logs improve SQLi testing
"""

import sys
import json
import asyncio
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from vapt.sqli_agent import SQLiAgent

def create_juice_shop_network_logs():
    """Create realistic network logs for Juice Shop"""
    return [
        {
            "url": "https://juice-shop.herokuapp.com/rest/user/login",
            "method": "POST",
            "status": 200,
            "headers": {
                "content-type": "application/json",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            "requestHeaders": {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            "postData": '{"email":"<EMAIL>","password":"admin123"}',
            "timestamp": "2025-07-19T05:30:20.000Z"
        },
        {
            "url": "https://juice-shop.herokuapp.com/rest/products/search?q=apple",
            "method": "GET",
            "status": 200,
            "headers": {
                "content-type": "application/json",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            "requestHeaders": {
                "Accept": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            "timestamp": "2025-07-19T05:30:25.000Z"
        },
        {
            "url": "https://juice-shop.herokuapp.com/api/Users/",
            "method": "POST",
            "status": 201,
            "headers": {
                "content-type": "application/json",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            "requestHeaders": {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            "postData": '{"email":"<EMAIL>","password":"password123","passwordRepeat":"password123","securityQuestion":{"id":1},"securityAnswer":"test"}',
            "timestamp": "2025-07-19T05:30:30.000Z"
        },
        {
            "url": "https://juice-shop.herokuapp.com/api/Feedbacks/",
            "method": "POST",
            "status": 201,
            "headers": {
                "content-type": "application/json",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            "requestHeaders": {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            "postData": '{"comment":"Great product!","rating":5,"captcha":"12","captchaId":1}',
            "timestamp": "2025-07-19T05:30:35.000Z"
        },
        {
            "url": "https://juice-shop.herokuapp.com/api/BasketItems/",
            "method": "POST",
            "status": 201,
            "headers": {
                "content-type": "application/json",
                "authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            "requestHeaders": {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            },
            "postData": '{"ProductId":1,"BasketId":"1","quantity":1}',
            "timestamp": "2025-07-19T05:30:40.000Z"
        }
    ]

def create_raw_requests():
    """Create corresponding raw requests"""
    return [
        """POST /rest/user/login HTTP/1.1\r
Host: juice-shop.herokuapp.com\r
Content-Type: application/json\r
Accept: application/json\r
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\r
Content-Length: 52\r
\r
{"email":"<EMAIL>","password":"admin123"}""",
        
        """GET /rest/products/search?q=apple HTTP/1.1\r
Host: juice-shop.herokuapp.com\r
Accept: application/json\r
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\r
\r
""",
        
        """POST /api/Users/<USER>/1.1\r
Host: juice-shop.herokuapp.com\r
Content-Type: application/json\r
Accept: application/json\r
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\r
Content-Length: 150\r
\r
{"email":"<EMAIL>","password":"password123","passwordRepeat":"password123","securityQuestion":{"id":1},"securityAnswer":"test"}"""
    ]

def create_test_preprocessor_data():
    """Create test preprocessor data with network logs"""
    return {
        "metadata": {
            "target_url": "https://juice-shop.herokuapp.com",
            "start_time": "2025-07-19T05:30:00.000000",
            "end_time": "2025-07-19T05:32:00.000000",
            "duration": "0:02:00.000000",
            "session_id": "test_network_logs"
        },
        "recon": [
            {
                "task": "Network Analysis",
                "response": "Identified multiple API endpoints with potential SQL injection points",
                "status": "completed"
            }
        ],
        "network_logs": create_juice_shop_network_logs(),
        "console_logs": [],
        "raw_requests": create_raw_requests(),
        "crawl_data": {
            "sitemap": {},
            "visited_urls": [],
            "orphan_pages": []
        },
        "component_interactions": [],
        "session_states": [],
        "navigation_paths": []
    }

async def test_sqli_with_network_logs():
    """Test SQLi agent with network logs"""
    print("🧪 Testing SQLi Agent with Network Logs")
    print("="*50)
    
    # Create SQLi agent
    sqli_agent = SQLiAgent()
    
    # Create test data
    preprocessor_data = create_test_preprocessor_data()
    
    print(f"📊 Test data created:")
    print(f"   - Network logs: {len(preprocessor_data['network_logs'])}")
    print(f"   - Raw requests: {len(preprocessor_data['raw_requests'])}")
    
    # Test injection point analysis
    print("\n🔍 Analyzing injection points...")
    raw_requests = preprocessor_data['raw_requests']
    network_logs = preprocessor_data['network_logs']
    
    injection_points = await sqli_agent._analyze_injection_points(raw_requests, network_logs)
    
    print(f"✅ Found {len(injection_points)} injection points:")
    for i, point in enumerate(injection_points, 1):
        print(f"   {i}. {point['type']} - {point['method']} {point.get('url', 'N/A')}")
        print(f"      Parameter: {point.get('parameter', 'N/A')} (Priority: {point.get('priority', 'N/A')})")
        if point.get('location') == 'JSON':
            print(f"      Content-Type: {point.get('content_type', 'N/A')}")
    
    # Test payload generation for high-priority points
    print("\n🎯 Testing high-priority injection points...")
    high_priority_points = [p for p in injection_points if p.get('priority') == 'high']
    
    for point in high_priority_points[:3]:  # Test first 3 high-priority points
        print(f"\n🔬 Testing: {point['parameter']} in {point['url']}")
        
        # Generate targeted payloads
        payloads = await sqli_agent._generate_targeted_payloads(point)
        print(f"   Generated {len(payloads)} targeted payloads")
        
        # Test first few payloads
        for payload in payloads[:3]:
            print(f"   Testing payload: {payload[:50]}...")
            result = await sqli_agent._test_targeted_payload(point, payload)
            if result:
                status = "🚨 VULNERABLE" if result.get('vulnerable') else "✅ Safe"
                print(f"   Result: {status} (Status: {result.get('status_code', 'N/A')})")
    
    print("\n📊 Summary:")
    print(f"   - Total injection points: {len(injection_points)}")
    print(f"   - High priority points: {len(high_priority_points)}")
    print(f"   - API endpoints: {len([p for p in injection_points if '/api/' in p.get('url', '') or '/rest/' in p.get('url', '')])}")
    print(f"   - JSON parameters: {len([p for p in injection_points if p.get('location') == 'JSON'])}")
    print(f"   - URL parameters: {len([p for p in injection_points if p.get('location') == 'GET'])}")

async def main():
    """Main test function"""
    try:
        await test_sqli_with_network_logs()
        
        print("\n🎯 Key Improvements:")
        print("   ✅ Network logs provide real API endpoints")
        print("   ✅ Prioritizes high-value parameters (email, password, id)")
        print("   ✅ Handles JSON and form data properly")
        print("   ✅ Skips static resources")
        print("   ✅ Focuses on API endpoints (/api/, /rest/)")
        print("   ✅ Includes proper headers for SQLMap")
        
        print("\n💡 This demonstrates why network logs are crucial:")
        print("   - Without network logs: Tests only main page URL")
        print("   - With network logs: Tests actual API endpoints where vulnerabilities exist")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
