{"metadata": {"target_url": "https://juice-shop.herokuapp.com", "start_time": "2025-07-19T05:30:00.000000", "end_time": "2025-07-19T05:32:00.000000", "duration": "0:02:00.000000", "session_id": "20250719_053000_complete"}, "recon": [{"task": "Initial Reconnaissance", "timestamp": "2025-07-19T05:30:15.000000", "response": "OWASP Juice Shop - Complete reconnaissance performed. Identified e-commerce SPA with Angular frontend, Express.js backend, multiple API endpoints for user management, product catalog, shopping cart, and administrative functions. Detected potential injection points in login, search, feedback, and user registration forms.", "status": "completed"}, {"task": "Comprehensive Site Crawl", "timestamp": "2025-07-19T05:30:45.000000", "response": "Discovered 25+ endpoints including REST API routes, authentication endpoints, file upload functionality, and administrative interfaces. Mapped complete application structure with user flows.", "status": "completed"}, {"task": "Component Interaction Testing", "timestamp": "2025-07-19T05:31:15.000000", "response": "Tested all interactive components: login forms, search functionality, product catalog, shopping cart, user registration, feedback submission, and file upload. Captured complete request/response cycles.", "status": "completed"}, {"task": "Authentication Flow Analysis", "timestamp": "2025-07-19T05:31:45.000000", "response": "Analyzed JWT-based authentication, session management, password reset flows, and administrative access controls. Identified multiple potential security issues.", "status": "completed"}], "network_logs": [{"url": "https://juice-shop.herokuapp.com/", "method": "GET", "status": 200, "statusText": "OK", "headers": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Dest": "document", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "Cache-Control": "max-age=0", "Sec-Ch-Ua": "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "\"Windows\""}, "requestHeaders": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Dest": "document", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "Cache-Control": "max-age=0", "Sec-Ch-Ua": "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "\"Windows\""}, "responseHeaders": {"content-type": "text/html", "server": "nginx/1.18.0", "x-powered-by": "Express"}, "timestamp": "2025-07-19T05:30:10.000Z", "responseSize": 1024}, {"url": "https://juice-shop.herokuapp.com/rest/products/search?q=", "method": "GET", "status": 200, "statusText": "OK", "headers": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Dest": "document", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "X-Requested-With": "XMLHttpRequest", "Sec-Ch-Ua": "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\"", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "\"Windows\""}, "requestHeaders": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Dest": "document", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "X-Requested-With": "XMLHttpRequest", "Sec-Ch-Ua": "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\"", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "\"Windows\""}, "responseHeaders": {"content-type": "text/html", "server": "nginx/1.18.0", "x-powered-by": "Express"}, "timestamp": "2025-07-19T05:30:11.000Z", "responseSize": 1124}, {"url": "https://juice-shop.herokuapp.com/rest/user/login", "method": "POST", "status": 200, "statusText": "OK", "headers": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "application/json, text/plain, */*", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Content-Type": "application/json", "Content-Length": "54", "Origin": "https://juice-shop.herokuapp.com", "Referer": "https://juice-shop.herokuapp.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "same-origin", "X-Requested-With": "XMLHttpRequest", "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...", "Sec-Ch-Ua": "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\""}, "requestHeaders": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "application/json, text/plain, */*", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Content-Type": "application/json", "Content-Length": "54", "Origin": "https://juice-shop.herokuapp.com", "Referer": "https://juice-shop.herokuapp.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "same-origin", "X-Requested-With": "XMLHttpRequest", "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...", "Sec-Ch-Ua": "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\""}, "responseHeaders": {"content-type": "text/html", "server": "nginx/1.18.0", "x-powered-by": "Express"}, "timestamp": "2025-07-19T05:30:12.000Z", "responseSize": 1224, "postData": "{\"email\": \"<EMAIL>\", \"password\": \"admin123\"}"}, {"url": "https://juice-shop.herokuapp.com/rest/products/search?q=apple", "method": "GET", "status": 200, "statusText": "OK", "headers": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Dest": "document", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "X-Requested-With": "XMLHttpRequest"}, "requestHeaders": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Dest": "document", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "X-Requested-With": "XMLHttpRequest"}, "responseHeaders": {"content-type": "text/html", "server": "nginx/1.18.0", "x-powered-by": "Express"}, "timestamp": "2025-07-19T05:30:13.000Z", "responseSize": 1324}, {"url": "https://juice-shop.herokuapp.com/api/Users/", "method": "POST", "status": 200, "statusText": "OK", "headers": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "application/json, text/plain, */*", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Content-Type": "application/json", "Content-Length": "195", "Origin": "https://juice-shop.herokuapp.com", "Referer": "https://juice-shop.herokuapp.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "same-origin"}, "requestHeaders": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "application/json, text/plain, */*", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Content-Type": "application/json", "Content-Length": "195", "Origin": "https://juice-shop.herokuapp.com", "Referer": "https://juice-shop.herokuapp.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "same-origin"}, "responseHeaders": {"content-type": "application/json", "server": "nginx/1.18.0", "x-powered-by": "Express"}, "timestamp": "2025-07-19T05:30:14.000Z", "responseSize": 1424, "postData": "{\"email\": \"<EMAIL>\", \"password\": \"password123\", \"passwordRepeat\": \"password123\", \"securityQuestion\": {\"id\": 1, \"question\": \"Your eldest siblings middle name?\"}, \"securityAnswer\": \"test\"}"}, {"url": "https://juice-shop.herokuapp.com/api/Feedbacks/", "method": "POST", "status": 200, "statusText": "OK", "headers": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "application/json, text/plain, */*", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Content-Type": "application/json", "Content-Length": "75", "Origin": "https://juice-shop.herokuapp.com", "Referer": "https://juice-shop.herokuapp.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "same-origin"}, "requestHeaders": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "application/json, text/plain, */*", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Content-Type": "application/json", "Content-Length": "75", "Origin": "https://juice-shop.herokuapp.com", "Referer": "https://juice-shop.herokuapp.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "same-origin"}, "responseHeaders": {"content-type": "application/json", "server": "nginx/1.18.0", "x-powered-by": "Express"}, "timestamp": "2025-07-19T05:30:15.000Z", "responseSize": 1524, "postData": "{\"comment\": \"Great product!\", \"rating\": 5, \"captcha\": \"12\", \"captchaId\": 1}"}, {"url": "https://juice-shop.herokuapp.com/profile", "method": "GET", "status": 200, "statusText": "OK", "headers": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Dest": "document", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."}, "requestHeaders": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Dest": "document", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."}, "responseHeaders": {"content-type": "text/html", "server": "nginx/1.18.0", "x-powered-by": "Express"}, "timestamp": "2025-07-19T05:30:16.000Z", "responseSize": 1624}, {"url": "https://juice-shop.herokuapp.com/api/BasketItems/", "method": "POST", "status": 200, "statusText": "OK", "headers": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "application/json, text/plain, */*", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Content-Type": "application/json", "Content-Length": "48", "Origin": "https://juice-shop.herokuapp.com", "Referer": "https://juice-shop.herokuapp.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "same-origin", "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."}, "requestHeaders": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "application/json, text/plain, */*", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Content-Type": "application/json", "Content-Length": "48", "Origin": "https://juice-shop.herokuapp.com", "Referer": "https://juice-shop.herokuapp.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "same-origin", "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."}, "responseHeaders": {"content-type": "application/json", "server": "nginx/1.18.0", "x-powered-by": "Express"}, "timestamp": "2025-07-19T05:30:17.000Z", "responseSize": 1724, "postData": "{\"ProductId\": 1, \"BasketId\": \"1\", \"quantity\": 1}"}, {"url": "https://juice-shop.herokuapp.com/file-upload", "method": "POST", "status": 200, "statusText": "OK", "headers": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "application/json, text/plain, */*", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Content-Type": "multipart/form-data; boundary=boundary123", "Content-Length": "152", "Origin": "https://juice-shop.herokuapp.com", "Referer": "https://juice-shop.herokuapp.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "same-origin"}, "requestHeaders": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "application/json, text/plain, */*", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Content-Type": "multipart/form-data; boundary=boundary123", "Content-Length": "152", "Origin": "https://juice-shop.herokuapp.com", "Referer": "https://juice-shop.herokuapp.com/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "same-origin"}, "responseHeaders": {"content-type": "text/html", "server": "nginx/1.18.0", "x-powered-by": "Express"}, "timestamp": "2025-07-19T05:30:18.000Z", "responseSize": 1824, "postData": "--boundary123\r\nContent-Disposition: form-data; name=\"file\"; filename=\"test.pdf\"\r\nContent-Type: application/pdf\r\n\r\n%PDF-1.4 test content\r\n--boundary123--"}, {"url": "https://juice-shop.herokuapp.com/administration", "method": "GET", "status": 200, "statusText": "OK", "headers": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Dest": "document", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."}, "requestHeaders": {"Host": "juice-shop.herokuapp.com", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Language": "en-US,en;q=0.9", "Accept-Encoding": "gzip, deflate, br", "Connection": "keep-alive", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Dest": "document", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "Authorization": "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9..."}, "responseHeaders": {"content-type": "text/html", "server": "nginx/1.18.0", "x-powered-by": "Express"}, "timestamp": "2025-07-19T05:30:19.000Z", "responseSize": 1924}], "console_logs": [{"level": "info", "message": "Angular application loaded successfully", "timestamp": "2025-07-19T05:30:05.000Z", "source": "console-api"}, {"level": "warning", "message": "Deprecated API endpoint used: /rest/user/login", "timestamp": "2025-07-19T05:30:20.000Z", "source": "console-api"}, {"level": "error", "message": "CORS policy violation detected", "timestamp": "2025-07-19T05:30:35.000Z", "source": "console-api"}], "raw_requests": ["GET / HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nSec-Fetch-Dest: document\r\nSec-Fetch-Mode: navigate\r\nSec-Fetch-Site: none\r\nCache-Control: max-age=0\r\nSec-Ch-Ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"\r\nSec-Ch-Ua-Mobile: ?0\r\nSec-Ch-Ua-Platform: \"Windows\"\r\n", "GET /rest/products/search?q= HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nSec-Fetch-Dest: document\r\nSec-Fetch-Mode: navigate\r\nSec-Fetch-Site: none\r\nX-Requested-With: XMLHttpRequest\r\nSec-Ch-Ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\"\r\nSec-Ch-Ua-Mobile: ?0\r\nSec-Ch-Ua-Platform: \"Windows\"\r\n", "POST /rest/user/login HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: application/json, text/plain, */*\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nContent-Type: application/json\r\nContent-Length: 54\r\nOrigin: https://juice-shop.herokuapp.com\r\nReferer: https://juice-shop.herokuapp.com/\r\nSec-Fetch-Dest: empty\r\nSec-Fetch-Mode: cors\r\nSec-Fetch-Site: same-origin\r\nX-Requested-With: XMLHttpRequest\r\nAuthorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...\r\nSec-Ch-Ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\"\r\n\r\n{\"email\": \"<EMAIL>\", \"password\": \"admin123\"}", "GET /rest/products/search?q=apple HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nSec-Fetch-Dest: document\r\nSec-Fetch-Mode: navigate\r\nSec-Fetch-Site: none\r\nX-Requested-With: XMLHttpRequest\r\n", "POST /api/Users/<USER>/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: application/json, text/plain, */*\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nContent-Type: application/json\r\nContent-Length: 195\r\nOrigin: https://juice-shop.herokuapp.com\r\nReferer: https://juice-shop.herokuapp.com/\r\nSec-Fetch-Dest: empty\r\nSec-Fetch-Mode: cors\r\nSec-Fetch-Site: same-origin\r\n\r\n{\"email\": \"<EMAIL>\", \"password\": \"password123\", \"passwordRepeat\": \"password123\", \"securityQuestion\": {\"id\": 1, \"question\": \"Your eldest siblings middle name?\"}, \"securityAnswer\": \"test\"}", "POST /api/Feedbacks/ HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: application/json, text/plain, */*\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nContent-Type: application/json\r\nContent-Length: 75\r\nOrigin: https://juice-shop.herokuapp.com\r\nReferer: https://juice-shop.herokuapp.com/\r\nSec-Fetch-Dest: empty\r\nSec-Fetch-Mode: cors\r\nSec-Fetch-Site: same-origin\r\n\r\n{\"comment\": \"Great product!\", \"rating\": 5, \"captcha\": \"12\", \"captchaId\": 1}", "GET /profile HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nSec-Fetch-Dest: document\r\nSec-Fetch-Mode: navigate\r\nSec-Fetch-Site: none\r\nAuthorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...\r\n", "POST /api/BasketItems/ HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: application/json, text/plain, */*\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nContent-Type: application/json\r\nContent-Length: 48\r\nOrigin: https://juice-shop.herokuapp.com\r\nReferer: https://juice-shop.herokuapp.com/\r\nSec-Fetch-Dest: empty\r\nSec-Fetch-Mode: cors\r\nSec-Fetch-Site: same-origin\r\nAuthorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...\r\n\r\n{\"ProductId\": 1, \"BasketId\": \"1\", \"quantity\": 1}", "POST /file-upload HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: application/json, text/plain, */*\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nContent-Type: multipart/form-data; boundary=boundary123\r\nContent-Length: 152\r\nOrigin: https://juice-shop.herokuapp.com\r\nReferer: https://juice-shop.herokuapp.com/\r\nSec-Fetch-Dest: empty\r\nSec-Fetch-Mode: cors\r\nSec-Fetch-Site: same-origin\r\n\r\n--boundary123\r\nContent-Disposition: form-data; name=\"file\"; filename=\"test.pdf\"\r\nContent-Type: application/pdf\r\n\r\n%PDF-1.4 test content\r\n--boundary123--", "GET /administration HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nSec-Fetch-Dest: document\r\nSec-Fetch-Mode: navigate\r\nSec-Fetch-Site: none\r\nAuthorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...\r\n", "GET /?id=%27+OR+%271%27%3D%271&search=%27+OR+%271%27%3D%271 HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nSec-Fetch-Dest: document\r\nSec-Fetch-Mode: navigate\r\nSec-Fetch-Site: none\r\n", "POST /login HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: application/json, text/plain, */*\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nContent-Type: application/json\r\nContent-Length: 47\r\nOrigin: https://juice-shop.herokuapp.com\r\nReferer: https://juice-shop.herokuapp.com/\r\nSec-Fetch-Dest: empty\r\nSec-Fetch-Mode: cors\r\nSec-Fetch-Site: same-origin\r\n\r\n{\"username\": \"' OR '1'='1\", \"password\": \"test\"}", "GET /?id=%27+OR+%271%27%3D%271%27--&search=%27+OR+%271%27%3D%271%27-- HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nSec-Fetch-Dest: document\r\nSec-Fetch-Mode: navigate\r\nSec-Fetch-Site: none\r\n", "POST /login HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: application/json, text/plain, */*\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nContent-Type: application/json\r\nContent-Length: 50\r\nOrigin: https://juice-shop.herokuapp.com\r\nReferer: https://juice-shop.herokuapp.com/\r\nSec-Fetch-Dest: empty\r\nSec-Fetch-Mode: cors\r\nSec-Fetch-Site: same-origin\r\n\r\n{\"username\": \"' OR '1'='1'--\", \"password\": \"test\"}", "GET /?id=%27+OR+%271%27%3D%271%27%2F%2A&search=%27+OR+%271%27%3D%271%27%2F%2A HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nSec-Fetch-Dest: document\r\nSec-Fetch-Mode: navigate\r\nSec-Fetch-Site: none\r\n", "GET /?q=%3Cscript%3Ealert%28%27XSS%27%29%3C%2Fscript%3E&search=%3Cscript%3Ealert%28%27XSS%27%29%3C%2Fscript%3E HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nSec-Fetch-Dest: document\r\nSec-Fetch-Mode: navigate\r\nSec-Fetch-Site: none\r\n", "POST /comment HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: application/json, text/plain, */*\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nContent-Type: application/json\r\nContent-Length: 85\r\nOrigin: https://juice-shop.herokuapp.com\r\nReferer: https://juice-shop.herokuapp.com/\r\nSec-Fetch-Dest: empty\r\nSec-Fetch-Mode: cors\r\nSec-Fetch-Site: same-origin\r\n\r\n{\"comment\": \"<script>alert('XSS')</script>\", \"name\": \"<script>alert('XSS')</script>\"}", "GET /?q=%3Cimg+src%3Dx+onerror%3Dalert%28%27XSS%27%29%3E&search=%3Cimg+src%3Dx+onerror%3Dalert%28%27XSS%27%29%3E HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nSec-Fetch-Dest: document\r\nSec-Fetch-Mode: navigate\r\nSec-Fetch-Site: none\r\n", "POST /comment HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: application/json, text/plain, */*\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nContent-Type: application/json\r\nContent-Length: 91\r\nOrigin: https://juice-shop.herokuapp.com\r\nReferer: https://juice-shop.herokuapp.com/\r\nSec-Fetch-Dest: empty\r\nSec-Fetch-Mode: cors\r\nSec-Fetch-Site: same-origin\r\n\r\n{\"comment\": \"<img src=x onerror=alert('XSS')>\", \"name\": \"<img src=x onerror=alert('XSS')>\"}", "GET /?q=%3Csvg+onload%3Dalert%28%27XSS%27%29%3E&search=%3Csvg+onload%3Dalert%28%27XSS%27%29%3E HTTP/1.1\r\nHost: juice-shop.herokuapp.com\r\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\r\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8\r\nAccept-Language: en-US,en;q=0.9\r\nAccept-Encoding: gzip, deflate, br\r\nConnection: keep-alive\r\nUpgrade-Insecure-Requests: 1\r\nSec-Fetch-Dest: document\r\nSec-Fetch-Mode: navigate\r\nSec-Fetch-Site: none\r\n"], "crawl_data": {"sitemap": {"/": "Main product catalog page", "/login": "User authentication", "/register": "User registration", "/profile": "User profile management", "/basket": "Shopping cart", "/administration": "Admin panel", "/rest/products/search": "Product search API", "/rest/user/login": "Login API", "/api/Users/": "User management API", "/api/Feedbacks/": "Feedback API", "/api/BasketItems/": "Cart management API"}, "visited_urls": ["https://juice-shop.herokuapp.com/", "https://juice-shop.herokuapp.com/login", "https://juice-shop.herokuapp.com/register", "https://juice-shop.herokuapp.com/profile", "https://juice-shop.herokuapp.com/basket", "https://juice-shop.herokuapp.com/administration", "https://juice-shop.herokuapp.com/rest/products/search", "https://juice-shop.herokuapp.com/rest/user/login", "https://juice-shop.herokuapp.com/api/Users/", "https://juice-shop.herokuapp.com/api/Feedbacks/"], "orphan_pages": ["https://juice-shop.herokuapp.com/ftp", "https://juice-shop.herokuapp.com/encryptionkeys"]}, "component_interactions": [{"type": "form_submit", "element": "login_form", "action": "/rest/user/login", "method": "POST", "timestamp": "2025-07-19T05:30:25.000Z"}, {"type": "input_fill", "element": "search_box", "value": "apple", "timestamp": "2025-07-19T05:30:40.000Z"}, {"type": "button_click", "element": "add_to_cart", "product_id": "1", "timestamp": "2025-07-19T05:31:00.000Z"}], "session_states": [{"timestamp": "2025-07-19T05:30:00.000Z", "authenticated": false, "cart_items": 0, "current_page": "/"}, {"timestamp": "2025-07-19T05:30:30.000Z", "authenticated": true, "user_id": "1", "cart_items": 0, "current_page": "/profile"}, {"timestamp": "2025-07-19T05:31:30.000Z", "authenticated": true, "user_id": "1", "cart_items": 2, "current_page": "/basket"}], "navigation_paths": [{"path": "/ -> /login -> /profile", "duration": "0:00:30", "successful": true}, {"path": "/profile -> /basket -> /administration", "duration": "0:01:00", "successful": false, "error": "Access denied"}]}